from sqlalchemy.orm import Session
from datetime import datetime
from sqlalchemy import and_

from models import User, Conversation, Message, Document, MessageFeedback

# 用户相关操作
def get_user(db: Session, user_id: int):
    """根据ID获取用户"""
    return db.query(User).filter(User.id == user_id).first()

def get_user_by_username(db: Session, username: str):
    """根据用户名获取用户"""
    return db.query(User).filter(User.username == username).first()

def get_user_by_email(db: Session, email: str):
    """根据邮箱获取用户"""
    return db.query(User).filter(User.email == email).first()

# 对话相关操作
def get_conversations(db: Session, user_id: int, skip: int = 0, limit: int = 100):
    """获取用户的所有对话"""
    return db.query(Conversation).filter(Conversation.user_id == user_id)\
        .order_by(Conversation.updated_at.desc())\
        .offset(skip).limit(limit).all()

def create_conversation(db: Session, user_id: int, title: str = "新对话"):
    """创建新对话"""
    db_conversation = Conversation(user_id=user_id, title=title)
    db.add(db_conversation)
    db.commit()
    db.refresh(db_conversation)
    return db_conversation

def get_conversation(db: Session, conversation_id: int):
    """获取单个对话"""
    return db.query(Conversation).filter(Conversation.id == conversation_id).first()

def update_conversation(db: Session, conversation_id: int, title: str):
    """更新对话标题"""
    db_conversation = get_conversation(db, conversation_id)
    if db_conversation:
        db_conversation.title = title
        db_conversation.updated_at = datetime.now()
        db.commit()
        db.refresh(db_conversation)
    return db_conversation

def delete_conversation(db: Session, conversation_id: int):
    """删除对话"""
    # 获取对话
    db_conversation = get_conversation(db, conversation_id)
    if db_conversation:
        try:
            # 先查询所有消息ID
            messages = db.query(Message.id).filter(Message.conversation_id == conversation_id).all()
            message_ids = [msg.id for msg in messages]
            
            # 删除消息反馈
            if message_ids:
                db.query(MessageFeedback).filter(MessageFeedback.message_id.in_(message_ids)).delete(synchronize_session=False)
            
            # 删除所有消息
            db.query(Message).filter(Message.conversation_id == conversation_id).delete(synchronize_session=False)
            
            # 最后删除对话
            db.delete(db_conversation)
            db.commit()
            return True
        except Exception as e:
            db.rollback()
            print(f"删除对话时发生错误: {str(e)}")
            return False
    return False

# 消息相关操作
def get_messages(db: Session, conversation_id: int, skip: int = 0, limit: int = 100):
    """获取对话中的所有消息"""
    return db.query(Message).filter(
        and_(
            Message.conversation_id == conversation_id,
            Message.status == 'active'  # 只获取活跃状态的消息
        )
    ).order_by(Message.created_at.asc())\
    .offset(skip).limit(limit).all()

def create_message(db: Session, conversation_id: int, role: str, content: str, file_paths: list = None, use_web: int = 0, parent_message_id: int = None, thinking: str = None):
    """创建新消息"""
    db_message = Message(
        conversation_id=conversation_id, 
        role=role, 
        content=content, 
        file_paths=file_paths, 
        use_web=use_web,
        parent_message_id=parent_message_id,
        thinking=thinking
    )
    db.add(db_message)
    
    # 更新对话的更新时间
    db_conversation = get_conversation(db, conversation_id)
    if db_conversation:
        db_conversation.updated_at = datetime.now()
    
    db.commit()
    db.refresh(db_message)
    return db_message

def get_message(db: Session, message_id: int):
    """获取单个消息"""
    return db.query(Message).filter(Message.id == message_id).first()

def edit_message(db: Session, message_id: int, new_content: str):
    """编辑消息（创建新消息并标记原消息为已编辑）"""
    # 获取原消息
    original_message = get_message(db, message_id)
    if not original_message:
        return None
    
    # 创建新消息
    new_message = create_message(
        db=db,
        conversation_id=original_message.conversation_id,
        role=original_message.role,
        content=new_content,
        file_paths=original_message.file_paths,
        use_web=original_message.use_web,
        parent_message_id=message_id  # 设置父消息ID
    )
    
    # 标记原消息为已编辑
    original_message.status = 'edited'
    db.commit()
    
    return new_message

# 消息反馈相关操作
def create_message_feedback(db: Session, message_id: int, user_id: int, feedback_type: str):
    """创建消息反馈"""
    # 检查是否已存在相同类型的反馈
    existing_feedback = db.query(MessageFeedback).filter(
        and_(
            MessageFeedback.message_id == message_id,
            MessageFeedback.user_id == user_id,
            MessageFeedback.feedback_type == feedback_type
        )
    ).first()
    
    if existing_feedback:
        return existing_feedback
    
    # 创建新反馈
    db_feedback = MessageFeedback(
        message_id=message_id,
        user_id=user_id,
        feedback_type=feedback_type
    )
    db.add(db_feedback)
    db.commit()
    db.refresh(db_feedback)
    return db_feedback

def get_message_feedback(db: Session, message_id: int, user_id: int):
    """获取用户对消息的反馈"""
    return db.query(MessageFeedback).filter(
        and_(
            MessageFeedback.message_id == message_id,
            MessageFeedback.user_id == user_id
        )
    ).all()

def delete_message_feedback(db: Session, message_id: int, user_id: int, feedback_type: str):
    """删除消息反馈"""
    feedback = db.query(MessageFeedback).filter(
        and_(
            MessageFeedback.message_id == message_id,
            MessageFeedback.user_id == user_id,
            MessageFeedback.feedback_type == feedback_type
        )
    ).first()
    
    if feedback:
        db.delete(feedback)
        db.commit()
        return True
    return False

# 文档相关操作
def create_document(db: Session, user_id: int, filename: str, file_path: str, content_type: str):
    """创建新文档记录"""
    db_document = Document(
        user_id=user_id,
        filename=filename,
        file_path=file_path,
        content_type=content_type
    )
    db.add(db_document)
    db.commit()
    db.refresh(db_document)
    return db_document

def get_document(db: Session, document_id: int):
    """获取单个文档记录"""
    return db.query(Document).filter(Document.id == document_id).first()

def get_user_documents(db: Session, user_id: int, skip: int = 0, limit: int = 100):
    """获取用户的所有文档"""
    return db.query(Document).filter(Document.user_id == user_id)\
        .order_by(Document.created_at.desc())\
        .offset(skip).limit(limit).all() 