/* 全局样式 */
:root {
    --primary-color: #3498db;
    --primary-light: #e2f0fd;
    --secondary-color: #2c3e50;
    --light-color: #f8f9fa;
    --border-color: #dee2e6;
    --text-color: #333;
    --user-msg-color: #d1e7ff;
    --ai-msg-color: #f1f1f1;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    background-color: #fff;
    margin: 0;
    padding: 0;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #999;
}

/* 按钮样式微调 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

/* 覆盖Bootstrap样式 */
.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
} 