{% extends "base.html" %}

{% block title %}登录 - 深圳能源AI助手{% endblock %}

{% block content %}
<div class="col-md-4 mx-auto mt-5">
    <div class="card shadow">
        <div class="card-header bg-primary text-white text-center">
            <h4>深圳能源AI助手</h4>
        </div>
        <div class="card-body">
            <!-- 登录表单 -->
            <form id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="loginUsername" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="loginPassword" required>
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">登录</button>
                </div>
                <div class="alert alert-danger mt-3 d-none" id="loginError"></div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            const errorDiv = document.getElementById('loginError');
            
            // 清除之前的错误
            errorDiv.textContent = '';
            errorDiv.classList.add('d-none');
            
            // 表单数据
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            
            // 发送登录请求
            axios.post('/api/v1/auth/token', formData)
                .then(function(response) {
                    // 保存token
                    localStorage.setItem('token', response.data.data.access_token);
                    // 跳转到聊天页面
                    window.location.href = '/chat';
                })
                .catch(function(error) {
                    errorDiv.textContent = error.response?.data?.message || '登录失败，请检查用户名和密码';
                    errorDiv.classList.remove('d-none');
                });
        });
    });
</script>
{% endblock %} 