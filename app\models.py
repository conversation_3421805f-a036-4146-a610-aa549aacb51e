from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON>ey, Integer, String, Text, DateTime, JSON, create_engine, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy.sql import func

from core.config import DATABASE_URL

# 创建数据库引擎
engine = create_engine(DATABASE_URL)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(255))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系
    conversations = relationship("Conversation", back_populates="user")
    event_logs = relationship("UserEventLog", back_populates="user")
    message_feedbacks = relationship("MessageFeedback", back_populates="user")

class Conversation(Base):
    """对话会话模型"""
    __tablename__ = "conversations"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255))
    user_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系
    user = relationship("User", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation")

class Message(Base):
    """消息模型"""
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"))
    role = Column(String(20))  # user 或 assistant
    content = Column(Text)
    file_paths = Column(JSON, nullable=True)  # 文件路径列表，使用JSON类型存储
    use_web = Column(Integer, default=0)  # 是否使用联网: 0不使用，1使用
    status = Column(Enum('active', 'edited', 'deleted'), default='active')  # 消息状态
    parent_message_id = Column(Integer, ForeignKey("messages.id"), nullable=True)  # 父消息ID，用于编辑历史
    thinking = Column(Text, nullable=True)  # AI思考过程
    created_at = Column(DateTime, default=func.now())

    # 关系
    conversation = relationship("Conversation", back_populates="messages")
    parent_message = relationship("Message", remote_side=[id], backref="edited_messages")
    feedbacks = relationship("MessageFeedback", back_populates="message")

class MessageFeedback(Base):
    """消息反馈模型"""
    __tablename__ = "message_feedback"

    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(Integer, ForeignKey("messages.id", ondelete="CASCADE"))
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"))
    feedback_type = Column(Enum('like', 'dislike', 'regenerate'), nullable=False)
    created_at = Column(DateTime, default=func.now())

    # 关系
    message = relationship("Message", back_populates="feedbacks")
    user = relationship("User", back_populates="message_feedbacks")

class Document(Base):
    """文档模型，用于存储上传的文件记录"""
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    filename = Column(String(255))  # 原始文件名
    file_path = Column(String(255))  # 服务器存储路径
    content_type = Column(String(100))  # 文件类型
    created_at = Column(DateTime, default=func.now())
    
    # 关系
    user = relationship("User")

class UserEventLog(Base):
    """统一用户事件日志：支持登录、访问、提问、上传等多种行为"""
    __tablename__ = "user_event_log"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"))
    event_type = Column(String(50), index=True)  # login, visit, question, upload...
    event_time = Column(DateTime, default=func.now(), index=True)

    # 可选字段
    page = Column(String(100), nullable=True)       # 访问页面
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    detail = Column(JSON, nullable=True)            # 行为细节：自定义字段（如上传文件名等）

    user = relationship("User", back_populates="event_logs") 