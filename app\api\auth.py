from datetime import timed<PERSON><PERSON>
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from models import get_db, User
from core.security import authenticate_user, create_access_token, get_current_active_user
from core.config import ACCESS_TOKEN_EXPIRE_MINUTES
from services.statistics_service import log_user_event
from core.response import success_response, error_response

from pydantic import BaseModel, Field

router = APIRouter()

class Token(BaseModel):
    access_token: str = Field(..., description="访问令牌，用于后续接口认证")
    token_type: str = Field(..., description="令牌类型，一般为bearer")

class UserResponse(BaseModel):
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="用户邮箱")
    is_active: bool = Field(..., description="用户是否激活")

@router.post("/token", response_model=Dict[str, Any], summary="获取访问令牌", description="通过用户名和密码获取访问令牌，用于后续接口认证")
def login_for_access_token(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取访问令牌
    
    - **username**: 用户名
    - **password**: 密码
    
    **返回参数说明**:
    - **code**: 状态码，200表示成功
    - **message**: 操作结果描述
    - **data**: 返回数据
      - **access_token**: 访问令牌，用于后续接口认证，需要在请求头中以 "Bearer {token}" 格式携带
      - **token_type**: 令牌类型，固定值为 "bearer"
    
    **返回示例**:
    ```json
    {
        "code": 200,
        "message": "登录成功",
        "data": {
            "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            "token_type": "bearer"
        }
    }
    ```
    """
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码不正确",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 记录用户登录日志
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("user-agent")
    log_user_event(
        db=db, 
        user_id=user.id, 
        event_type="login",
        page="login",
        ip_address=ip_address, 
        user_agent=user_agent
    )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    data = {"access_token": access_token, "token_type": "bearer"}
    return success_response(data=data, message="登录成功")

@router.get("/me", response_model=Dict[str, Any], summary="获取当前用户信息", description="获取当前已认证用户的详细信息")
def read_users_me(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取当前用户信息
    
    **需要认证**: 此接口需要Bearer Token认证
    
    **返回参数说明**:
    - **code**: 状态码，200表示成功
    - **message**: 操作结果描述
    - **data**: 返回数据
      - **id**: 用户ID，唯一标识
      - **username**: 用户名
      - **email**: 用户邮箱地址
      - **is_active**: 用户账号是否处于激活状态，true表示激活，false表示禁用
    
    **返回示例**:
    ```json
    {
        "code": 200,
        "message": "操作成功",
        "data": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "is_active": true
        }
    }
    ```
    """
    data = {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "is_active": current_user.is_active
    }
    return success_response(data=data) 