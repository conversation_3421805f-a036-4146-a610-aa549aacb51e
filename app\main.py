from fastapi import FastAP<PERSON>, Request, APIRouter, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse, HTMLResponse
from fastapi.exceptions import RequestValidationError
from typing import Optional
import os
from pathlib import Path

from api.auth import router as auth_router
from api.chat import router as chat_router
from api.statistics import router as statistics_router
from api.track import router as track_router
from core.response import http_exception_handler, error_response

api_router = APIRouter(prefix="/api/v1")

api_router.include_router(auth_router, prefix="/auth", tags=["认证"])
api_router.include_router(chat_router, prefix="/chat", tags=["聊天"])
api_router.include_router(statistics_router, prefix="/statistics", tags=["统计"])
api_router.include_router(track_router, prefix="/track", tags=["埋点"])

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent
STATIC_DIR = os.path.join(Path(__file__).resolve().parent, "static")
TEMPLATES_DIR = os.path.join(BASE_DIR, "app", "templates")

# 创建FastAPI应用
app = FastAPI(
    title="深圳能源AI助手",
    description="""
    # 深圳能源AI助手API文档
    
    本文档提供了深圳能源AI助手的所有接口说明与使用方法。
    
    ## 主要功能模块
    
    * **认证模块** - 用户登录与认证
    * **聊天模块** - 对话创建、消息收发、文件管理
    * **统计模块** - 访问统计、提问统计、用户统计
    
    ## 认证说明
    
    除登录接口外，所有接口都需要先获取令牌并在请求头中携带：
    ```
    Authorization: Bearer {token}
    ```

    ## 响应格式
    所有接口均使用统一的响应格式：
    ```json
    {
        "code": 200,           // 状态码，200表示成功
        "message": "操作成功",   // 操作结果描述
        "data": { ... }        // 响应数据
    }
    ```
    """,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json"
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

# 配置模板
templates = Jinja2Templates(directory=TEMPLATES_DIR)

# 允许跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应设置为特定的前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(api_router)

# 注册异常处理器
@app.exception_handler(HTTPException)
async def custom_http_exception_handler(request: Request, exc: HTTPException):
    return http_exception_handler(exc.status_code, exc.detail)

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return error_response(message="请求参数验证失败", code=422, data=str(exc))

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    return error_response(message="服务器内部错误", code=500, data=str(exc))

# 处理favicon.ico
@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    return RedirectResponse(url="/static/images/favicon.ico")

# 重定向根路径到登录页面
@app.get("/", response_class=HTMLResponse)
async def redirect_to_login():
    return RedirectResponse(url="/login")

# 登录页面
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# 聊天页面（需要登录）
@app.get("/chat", response_class=HTMLResponse)
async def chat_page(request: Request, token: Optional[str] = None):
    # 这里简单通过渲染模板返回，实际的权限检查在前端完成
    return templates.TemplateResponse("chat.html", {"request": request})

# 统计看板页面（需要登录）
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard_page(request: Request, token: Optional[str] = None):
    # 这里简单通过渲染模板返回，实际的权限检查在前端完成
    return templates.TemplateResponse("dashboard.html", {"request": request})

# 启动服务器
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=7000, reload=True)