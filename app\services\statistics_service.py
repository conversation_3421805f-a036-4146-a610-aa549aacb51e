from datetime import date, datetime, timedelta
from sqlalchemy import func
from sqlalchemy.orm import Session

from models import User, Message, UserEventLog

def get_user_summary(db: Session):
    """获取用户统计汇总数据"""
    # 系统注册用户总数
    total_accounts = db.query(func.count(User.id)).scalar()
    
    # 历史上至少登录过一次的用户数量
    total_logged_in = db.query(UserEventLog.user_id).filter(
        UserEventLog.event_type == "login"
    ).distinct().count()
    
    # 今天登录的用户数量
    today_logged_in = db.query(UserEventLog.user_id).filter(
        UserEventLog.event_type == "login",
        func.date(UserEventLog.event_time) == date.today()
    ).distinct().count()
    
    # 当前在线用户数量（30分钟内有活动的用户）
    online_cutoff = datetime.utcnow() - timedelta(minutes=30)
    online_users = db.query(UserEventLog.user_id).filter(
        UserEventLog.event_time > online_cutoff
    ).distinct().count()
    
    return {
        "total_accounts": total_accounts,
        "total_logged_in": total_logged_in,
        "today_logged_in": today_logged_in,
        "online": online_users
    }

def get_visits_summary(db: Session):
    """获取访问统计汇总数据"""
    # 累计访问次数 - 只统计chat页面访问
    total_visits = db.query(UserEventLog).filter(
        UserEventLog.event_type == "visit",
        UserEventLog.page == "chat"
    ).count()
    
    # 今日访问次数 - 只统计chat页面访问
    today_visits = db.query(UserEventLog).filter(
        UserEventLog.event_type == "visit",
        UserEventLog.page == "chat",
        func.date(UserEventLog.event_time) == date.today()
    ).count()
    
    return {
        "total": total_visits,
        "today": today_visits
    }

def get_questions_summary(db: Session):
    """获取提问统计汇总数据"""
    # 累计提问次数 (只统计用户的消息)
    total_questions = db.query(Message).filter(Message.role == "user").count()
    
    # 今日提问次数
    today_questions = db.query(Message).filter(
        Message.role == "user",
        func.date(Message.created_at) == date.today()
    ).count()
    
    return {
        "total": total_questions,
        "today": today_questions
    }

def get_trend_data(db: Session, group_by: str, start_date: date, end_date: date):
    """获取平台访问量和提问量的趋势数据
    
    Args:
        db: 数据库会话
        group_by: 分组方式，可选值为 hour(按小时)、day(按天)或month(按月)
        start_date: 起始日期
        end_date: 结束日期
        
    Returns:
        趋势数据列表
        
    Raises:
        ValueError: 如果group_by参数无效，抛出异常
    """
    trend_data = []
    
    # 参数验证
    valid_group_by = ['hour', 'day', 'month']
    if group_by not in valid_group_by:
        raise ValueError(f"无效的group_by参数: {group_by}。有效值为: {', '.join(valid_group_by)}")
    
    # 按小时分组
    if group_by == "hour":
        current_date = start_date
        
        while current_date <= end_date:
            for hour in range(0, 24):
                # 构建时间范围
                hour_start = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=hour)
                hour_end = hour_start + timedelta(hours=1)
                
                # 提问量统计
                question_count = db.query(Message).filter(
                    Message.role == "user",
                    Message.created_at >= hour_start,
                    Message.created_at < hour_end
                ).count()
                
                # 访问量统计 - 只统计chat页面访问
                visit_count = db.query(UserEventLog).filter(
                    UserEventLog.event_type == "visit",
                    UserEventLog.page == "chat",
                    UserEventLog.event_time >= hour_start,
                    UserEventLog.event_time < hour_end
                ).count()
                
                # 添加到结果列表
                trend_data.append({
                    "time": hour_start.strftime("%Y-%m-%d %H:00"),
                    "questions": question_count,
                    "visits": visit_count
                })
                
            current_date += timedelta(days=1)
    # 按天分组
    elif group_by == "day":
        current_date = start_date
        
        while current_date <= end_date:
            # 构建时间范围
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = day_start + timedelta(days=1)
            
            # 提问量统计
            question_count = db.query(Message).filter(
                Message.role == "user",
                Message.created_at >= day_start,
                Message.created_at < day_end
            ).count()
            
            # 访问量统计 - 只统计chat页面访问
            visit_count = db.query(UserEventLog).filter(
                UserEventLog.event_type == "visit",
                UserEventLog.page == "chat",
                UserEventLog.event_time >= day_start,
                UserEventLog.event_time < day_end
            ).count()
            
            # 添加到结果列表
            trend_data.append({
                "time": day_start.strftime("%Y-%m-%d"),
                "questions": question_count,
                "visits": visit_count
            })
            
            current_date += timedelta(days=1)
    # 按月分组
    elif group_by == "month":
        # 获取年月范围
        start_year, start_month = start_date.year, start_date.month
        end_year, end_month = end_date.year, end_date.month
        
        current_year, current_month = start_year, start_month
        
        while (current_year < end_year) or (current_year == end_year and current_month <= end_month):
            # 获取当月第一天
            month_start = date(current_year, current_month, 1)
            
            # 获取下月第一天作为当月结束
            if current_month == 12:
                next_month_start = date(current_year + 1, 1, 1)
            else:
                next_month_start = date(current_year, current_month + 1, 1)
                
            # 构建时间范围
            month_start_dt = datetime.combine(month_start, datetime.min.time())
            month_end_dt = datetime.combine(next_month_start, datetime.min.time())
            
            # 提问量统计
            question_count = db.query(Message).filter(
                Message.role == "user",
                Message.created_at >= month_start_dt,
                Message.created_at < month_end_dt
            ).count()
            
            # 访问量统计 - 只统计chat页面访问
            visit_count = db.query(UserEventLog).filter(
                UserEventLog.event_type == "visit",
                UserEventLog.page == "chat",
                UserEventLog.event_time >= month_start_dt,
                UserEventLog.event_time < month_end_dt
            ).count()
            
            # 添加到结果列表
            trend_data.append({
                "time": month_start_dt.strftime("%Y-%m"),
                "questions": question_count,
                "visits": visit_count
            })
            
            # 更新下一个月
            if current_month == 12:
                current_year += 1
                current_month = 1
            else:
                current_month += 1
    
    return trend_data

def log_user_event(db: Session, user_id: int, event_type: str, page: str = None, 
                  ip_address: str = None, user_agent: str = None, detail: dict = None):
    """记录用户事件
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        event_type: 事件类型，如visit, question, upload等
        page: 页面路径
        ip_address: 用户IP地址
        user_agent: 用户浏览器信息
        detail: 事件详情(JSON格式)
    """
    event_log = UserEventLog(
        user_id=user_id,
        event_type=event_type,
        page=page,
        ip_address=ip_address,
        user_agent=user_agent,
        detail=detail
    )
    db.add(event_log)
    db.commit()
    return event_log 