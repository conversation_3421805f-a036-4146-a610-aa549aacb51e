from typing import Any, Dict, Generic, Optional, TypeVar, Union
from pydantic import BaseModel, Field
from fastapi.responses import JSONResponse
from fastapi import status

# 定义泛型类型变量
T = TypeVar('T')

class StandardResponse(BaseModel, Generic[T]):
    """标准响应模型，用于统一API响应格式"""
    code: int = Field(200, description="状态码，200表示成功")
    message: str = Field("操作成功", description="操作结果描述")
    data: Optional[T] = Field(None, description="响应数据")

def success_response(data: Any = None, message: str = "操作成功", status_code: int = 200) -> Dict[str, Any]:
    """返回成功的标准响应格式"""
    return {
        "code": status_code,
        "message": message,
        "data": data
    }

def error_response(message: str = "操作失败", code: int = 400, data: Any = None) -> JSONResponse:
    """返回错误的标准响应格式"""
    return JSONResponse(
        status_code=code,
        content={
            "code": code,
            "message": message,
            "data": data
        }
    )

# HTTP异常处理，将FastAPI的HTTPException转换为标准响应格式
def http_exception_handler(status_code: int, detail: Any) -> JSONResponse:
    """将HTTP异常转换为标准格式响应"""
    return JSONResponse(
        status_code=status_code,
        content={
            "code": status_code,
            "message": str(detail),
            "data": None
        }
    ) 