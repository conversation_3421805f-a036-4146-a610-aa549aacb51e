# 深圳能源AI助手

基于FastAPI和大模型API开发的智能问答系统，帮助用户快速获取信息和解决问题。

## 项目介绍

深圳能源AI助手是一个基于Python FastAPI开发的智能对话系统，使用大模型API提供智能回答功能。系统支持多轮对话、对话历史管理、用户认证等功能。

### 核心功能

- 用户认证和权限管理：支持用户注册、登录和JWT认证
- 多轮智能对话：保持上下文的连贯对话体验
- 对话历史保存和管理：创建、查看、修改和删除对话
- 大模型API集成：灵活接入各种大模型API
- 预设任务功能：支持"新闻搜索"和"帮我写作"等预设任务模式
- 文件上传和处理：支持文件上传与对话关联
- 数据统计分析：提供平台使用趋势和汇总统计
- 问题复制与编辑：支持复制历史问题并编辑重新提问
- 回答交互功能：支持对AI回答进行点赞、差评和重新生成

### 技术特点

- 统一的API响应格式：所有接口使用相同的返回格式，方便前端处理
- 健壮的错误处理：全局异常捕获和格式化输出
- 安全的文件处理：防止文件名攻击和冲突
- 文档完善：详细的API文档和使用说明

## 技术栈

- 后端：Python + FastAPI
- 前端：HTML + CSS + JavaScript + Bootstrap 5
- 数据库：MySQL
- 认证：JWT（JSON Web Token）
- AI接口：兼容OpenAI格式的大模型API

## 项目结构

```
senai-core/
├── app/                    # 主应用目录
│   ├── api/                # API接口
│   │   ├── auth.py         # 认证相关API
│   │   ├── chat.py         # 聊天相关API
│   │   ├── statistics.py   # 统计相关API
│   │   └── track.py        # 用户行为埋点API
│   ├── core/               # 核心模块
│   │   ├── config.py       # 配置文件
│   │   └── security.py     # 安全相关
│   ├── services/           # 服务层
│   │   ├── ai_service.py   # AI服务
│   │   └── db_service.py   # 数据库服务
│   ├── static/             # 静态文件
│   │   ├── css/            # 样式文件
│   │   ├── js/             # JavaScript文件
│   │   └── images/         # 图片资源
│   ├── templates/          # 模板文件
│   │   ├── base.html       # 基础模板
│   │   ├── login.html      # 登录页面
│   │   └── chat.html       # 聊天页面
│   ├── uploads/            # 上传文件存储目录
│   ├── models.py           # 数据库模型定义
│   └── main.py             # 主应用入口
├── .env                    # 环境变量配置
├── ddl.sql                 # 数据库结构定义
├── requirements.txt        # 项目依赖
└── README.md               # 项目说明
```

## 安装和运行



### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 配置环境变量

在项目根目录创建 `.env` 文件：

```
DATABASE_URL=mysql+pymysql://user:password@localhost/senai_db
SECRET_KEY=your-secret-key
LLM_API_URL=https://your-api-url
LLM_API_KEY=your-api-key
```

注意：
- 数据库URL格式为：`mysql+pymysql://用户名:密码@主机地址/数据库名`
- 建议修改SECRET_KEY为随机生成的密钥
- 请填写正确的大模型API地址和授权密钥

对于MySQL数据库，使用提供的DDL文件初始化：

在MySQL中创建数据库：

```sql
CREATE DATABASE senai_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 6. 运行应用

```bash
python -m app.main
```

访问 http://localhost:8000 即可使用应用。

## 接口说明

所有接口均采用统一的响应格式：

```json
{
    "code": 200,           // 状态码，200表示成功
    "message": "操作成功",    // 操作结果描述
    "data": { ... }        // 响应数据，根据接口不同而变化
}
```

错误响应格式：

```json
{
    "code": 400,           // 错误状态码
    "message": "错误描述",    // 具体错误信息
    "data": null           // 错误时通常为null，有时包含额外错误信息
}
```

### 1. 认证接口

- `POST /api/v1/auth/token`：获取访问令牌
  - 请求参数：username(用户名)、password(密码)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "登录成功",
      "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6...",
        "token_type": "bearer"
      }
    }
    ```

- `GET /api/v1/auth/me`：获取当前用户信息
  - 请求参数：无（需要Bearer Token认证）
  - 返回：
    ```json
    {
      "code": 200,
      "message": "操作成功",
      "data": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "is_active": true
      }
    }
    ```

### 2. 对话接口

- `GET /api/v1/chat/conversations`：获取对话列表
  - 请求参数：skip(分页起始)、limit(每页数量)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "操作成功",
      "data": [
        {
          "id": 1,
          "title": "关于项目规划的讨论",
          "created_at": "2023-01-01T12:00:00",
          "updated_at": "2023-01-01T13:30:00"
        },
        ...
      ]
    }
    ```

- `POST /api/v1/chat/conversations`：创建新对话
  - 请求参数：title(对话标题，可选)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "对话创建成功",
      "data": {
        "id": 3,
        "title": "新对话",
        "created_at": "2023-01-03T15:00:00",
        "updated_at": "2023-01-03T15:00:00"
      }
    }
    ```

- `GET /api/v1/chat/conversations/{conversation_id}`：获取对话详情
  - 请求参数：conversation_id(对话ID，路径参数)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "操作成功",
      "data": {
        "id": 1,
        "title": "关于项目规划的讨论",
        "created_at": "2023-01-01T12:00:00",
        "updated_at": "2023-01-01T13:30:00",
        "messages": [
          {
            "id": 1,
            "role": "user",
            "content": "你好，我想讨论一下项目规划",
            "created_at": "2023-01-01T12:00:00",
            "file_path": null,
            "use_web": 0,
            "status": "active",
            "parent_message_id": null
          },
          ...
        ]
      }
    }
    ```

- `PUT /api/v1/chat/conversations/{conversation_id}`：更新对话标题
  - 请求参数：conversation_id(对话ID，路径参数)、title(新标题)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "对话标题更新成功",
      "data": {
        "id": 1,
        "title": "更新后的标题",
        "created_at": "2023-01-01T12:00:00",
        "updated_at": "2023-01-01T14:00:00"
      }
    }
    ```

- `DELETE /api/v1/chat/conversations/{conversation_id}`：删除对话
  - 请求参数：conversation_id(对话ID，路径参数)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "对话已删除",
      "data": null
    }
    ```

- `POST /api/v1/chat/messages/send`：发送消息并获取AI回复
  - 请求参数：
    - conversation_id(对话ID，路径参数)
    - content(消息内容)
    - document_id(关联文档ID，可选)
    - file_path(文件路径，可选)
    - use_web(是否使用网络搜索，可选，默认0)
    - system_prompt(系统提示词，可选，用于设置AI回复的角色和风格)
  - 系统提示词说明：
    - 系统提示词会作为一条system角色的消息添加到对话历史的开头
    - 预设功能模式会自动设置对应的系统提示词：
      - 新闻搜索: "你是一个新闻信息助手，请基于事实和公开报道简明扼要地回答用户的问题。"
      - 帮我写作: "你是一名专业写作助手，请根据用户要求撰写高质量文本，注重语言流畅、结构清晰、内容完整。"
  - 成功返回：
    ```json
    {
      "code": 200,
      "message": "消息发送成功",
      "data": {
        "user_message": {
          "id": 3,
          "role": "user",
          "content": "如何优化项目性能？",
          "created_at": "2023-01-01T14:30:00",
          "file_path": null,
          "use_web": 0,
          "status": "active",
          "parent_message_id": null
        },
        "ai_message": {
          "id": 4,
          "role": "assistant",
          "content": "优化项目性能可以从以下几个方面考虑：...",
          "created_at": "2023-01-01T14:30:10",
          "file_path": null,
          "use_web": 0,
          "status": "active",
          "parent_message_id": null
        }
      }
    }
    ```
  - 失败返回：
    ```json
    {
      "code": 500,
      "message": "AI响应失败: 服务暂时不可用",
      "data": {
        "user_message": {
          "id": 5,
          "role": "user",
          "content": "如何优化项目性能？",
          "created_at": "2023-01-01T15:30:00",
          "file_path": null,
          "use_web": 0,
          "status": "active",
          "parent_message_id": null
        }
      }
    }
    ```

- `PUT /api/v1/chat/messages/{message_id}`：编辑消息
  - 请求参数：
    - message_id(消息ID，路径参数)
    - content(新的消息内容)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "消息编辑成功",
      "data": {
        "id": 5,
        "role": "user",
        "content": "编辑后的消息内容",
        "created_at": "2023-01-01T15:00:00",
        "file_path": null,
        "use_web": 0,
        "status": "active",
        "parent_message_id": 3
      }
    }
    ```

- `DELETE /api/v1/chat/messages/{message_id}`：删除消息（软删除）
  - 请求参数：message_id(消息ID，路径参数)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "消息已删除",
      "data": null
    }
    ```

- `POST /api/v1/chat/messages/{message_id}/feedback`：添加消息反馈
  - 请求参数：
    - message_id(消息ID，路径参数)
    - feedback_type(反馈类型：like-点赞，dislike-差评，regenerate-重新生成)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "反馈已记录",
      "data": {
        "message_id": 4,
        "user_id": 1,
        "feedback_type": "like",
        "created_at": "2023-01-01T16:00:00"
      }
    }
    ```

- `POST /api/v1/chat/messages/{message_id}/regenerate`：重新生成AI回答
  - 请求参数：message_id(消息ID，路径参数)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "回答已重新生成",
      "data": {
        "id": 6,
        "role": "assistant",
        "content": "重新生成的回答内容...",
        "created_at": "2023-01-01T17:00:00",
        "file_path": null,
        "use_web": 0,
        "status": "active",
        "parent_message_id": null
      }
    }
    ```

- `POST /api/v1/chat/upload`：上传文件
  - 请求参数：file(要上传的文件)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "文件上传成功",
      "data": {
        "id": 1,
        "filename": "example.pdf",
        "file_path": "uploads/1_20230115123045_example.pdf",
        "content_type": "application/pdf"
      }
    }
    ```

### 3. 统计接口

- `GET /api/v1/statistics/trend`：获取平台使用趋势数据
  - 请求参数：
    - group_by(分组方式，hour或day)
    - start_date(起始日期，可选，默认最近7天)
    - end_date(结束日期，可选，默认当天)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "操作成功",
      "data": [
        {
          "time": "2023-01-01 00:00",
          "visits": 120,
          "questions": 45
        },
        ...
      ]
    }
    ```

- `GET /api/v1/statistics/summary`：获取平台统计汇总
  - 请求参数：module(模块名，可选，用于筛选特定模块数据)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "操作成功",
      "data": {
        "visits": {
          "total": 5000,
          "today": 120
        },
        "questions": {
          "total": 2000,
          "today": 50
        },
        "users": {
          "total_accounts": 500,
          "total_logged_in": 300,
          "today_logged_in": 30,
          "online": 10
        },
        "stat_date": "2023-01-15"
      }
    }
    ```

### 4. 埋点接口

- `POST /api/v1/track/visit`：记录页面访问事件
  - 请求参数：
    - page(页面标识，例如chat、login等)
    - detail(事件详情，可选)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "事件记录成功",
      "data": null
    }
    ```

- `POST /api/v1/track/event`：记录自定义事件
  - 请求参数：
    - event_type(事件类型，例如click、search等)
    - page(页面标识)
    - detail(事件详情，可选)
  - 返回：
    ```json
    {
      "code": 200,
      "message": "事件记录成功",
      "data": null
    }
    ```

## 前端页面

1. **登录页面** `/login`
   - 用户登录和注册功能

2. **聊天页面** `/chat`
   - 多轮对话界面
   - 对话历史管理
   - 预设任务功能选择（新闻搜索、帮我写作）
   - 文件上传功能
   - 问题复制与编辑功能
   - 回答交互功能（点赞、差评、重新生成）

## 数据库架构

系统使用关系型数据库（MySQL）存储数据，主要包含以下表：

1. **users** - 用户信息
   - 存储用户账号、密码和基本信息
   
2. **conversations** - 对话会话
   - 存储用户创建的对话会话信息
   
3. **messages** - 对话消息
   - 存储对话中的所有消息内容
   - 支持消息状态管理（active-活跃，edited-已编辑，deleted-已删除）
   - 支持消息编辑历史追踪（通过parent_message_id关联）
   - 支持存储AI思考过程（thinking字段）
   
4. **message_feedback** - 消息反馈
   - 存储用户对消息的反馈信息
   - 支持点赞、差评、重新生成等反馈类型
   
5. **documents** - 上传的文档信息
   - 存储用户上传的文件信息

6. **user_event_log** - 统一用户事件日志
   - 支持记录各类用户事件，包括登录、访问、提问、上传等
   - 统一的事件记录结构，便于扩展和分析
   - 支持自定义事件详情存储（JSON格式）
   - 用于统计用户登录次数、活跃用户数、访问量等各类指标

详细的数据库结构可查看 `ddl.sql` 文件。