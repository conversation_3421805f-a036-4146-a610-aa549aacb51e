
# 基于 SenseVoice 的语音转文字问答系统（后端 + 前端）

本说明文档展示如何使用 SenseVoice 和 GPT 实现语音识别 + 文本问答功能，并提供浏览器前端录音上传实现方式。

---

## ✅ 一、后端目标流程（无需前端）

用户上传语音文件（如 wav/webm） → 后端用 SenseVoice 转文字 → 调用 GPT 接口生成回答 → 返回 JSON。

### 项目结构示例：

```
backend/
├── main.py             # FastAPI 主服务
├── asr_sensevoice.py   # SenseVoice 封装
├── llm_gpt.py          # GPT 封装
└── test.wav            # 本地测试音频
```

### 示例代码：

#### 🔹 SenseVoice 封装：`asr_sensevoice.py`

```python
from funasr import AutoModel
import torchaudio

model = AutoModel(
    model="iic/SenseVoiceSmall",
    vad_model="iic/speech_fsmn_vad_zh-cn-16k-common-pytorch",
    trust_remote_code=True
)

def transcribe(file_path: str) -> str:
    audio, sr = torchaudio.load(file_path)
    text = model.generate(input=audio[0].numpy(), cache={}, language="auto")[0]["text"]
    return text
```

#### 🔹 GPT 封装（OpenAI）：`llm_gpt.py`

```python
import openai

openai.api_key = "你的 OpenAI 密钥"

def ask_gpt(prompt: str) -> str:
    response = openai.ChatCompletion.create(
        model="gpt-3.5-turbo",
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content.strip()
```

#### 🔹 FastAPI 主服务：`main.py`

```python
from fastapi import FastAPI, File, UploadFile
from asr_sensevoice import transcribe
from llm_gpt import ask_gpt
import shutil

app = FastAPI()

@app.post("/asr-chat/")
async def asr_chat(audio: UploadFile = File(...)):
    temp_path = f"temp_{audio.filename}"
    with open(temp_path, "wb") as buffer:
        shutil.copyfileobj(audio.file, buffer)

    text = transcribe(temp_path)
    reply = ask_gpt(text)

    return {"transcription": text, "response": reply}
```

---

## ✅ 二、前端：浏览器实现语音录音并上传

### ✅ 实现原理：使用 `MediaRecorder` API

用户点击按钮 → 浏览器录音 → 保存为 Blob（webm） → 通过 `FormData` 上传到后端。

### ✅ 示例 HTML + JS 代码

```html
<button id="start">🎙️ 开始录音</button>
<button id="stop" disabled>🛑 停止并发送</button>

<script>
let mediaRecorder;
let audioChunks = [];

document.getElementById('start').onclick = async () => {
  const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
  mediaRecorder = new MediaRecorder(stream);
  audioChunks = [];

  mediaRecorder.ondataavailable = event => {
    if (event.data.size > 0) {
      audioChunks.push(event.data);
    }
  };

  mediaRecorder.onstop = async () => {
    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
    const formData = new FormData();
    formData.append('audio', audioBlob, 'voice.webm');

    const res = await fetch('/asr-chat/', {
      method: 'POST',
      body: formData
    });

    const json = await res.json();
    console.log('识别内容:', json.transcription);
    console.log('GPT回复:', json.response);
  };

  mediaRecorder.start();
  document.getElementById('start').disabled = true;
  document.getElementById('stop').disabled = false;
};

document.getElementById('stop').onclick = () => {
  mediaRecorder.stop();
  document.getElementById('start').disabled = false;
  document.getElementById('stop').disabled = true;
};
</script>
```

---

## ✅ 三、后端兼容 `.webm` 的处理方式

推荐上传 `.webm` 后在服务端用 `ffmpeg` 转成 `wav`：

```python
import subprocess

def convert_webm_to_wav(webm_path, wav_path):
    subprocess.run(["ffmpeg", "-i", webm_path, "-ar", "16000", "-ac", "1", wav_path])
```

---

## ✅ 总结

| 模块 | 功能 |
|------|------|
| 🎙️ 前端录音 | 使用 `MediaRecorder` 录制 webm |
| 🔁 上传接口 | POST `/asr-chat/` |
| 🧠 ASR 识别 | SenseVoice 本地识别 |
| 🤖 GPT 回答 | 本地或 OpenAI 回答文字 |
| ✅ 整体闭环 | 语音 → 文字 → 回答 |

你可以在本地使用 curl 或 HTML 页面测试接口，自测完全闭环无需前端部署。

