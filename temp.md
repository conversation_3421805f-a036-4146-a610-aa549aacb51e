✅ 功能名称：预设任务型功能按钮（写死）

提供两个功能按钮，分别是 「新闻搜索」 和 「帮我写作」，点击后引导用户以不同的方式使用同一个大模型，实现不同风格的对话体验。
📌 功能一：「新闻搜索」
功能目的：

引导用户提出搜索类问题，例如“最近的新能源政策”、“华为发布了什么新产品”等，模型将以信息摘要助手的角色回答，回答风格偏简洁、事实型、结构化。
前端行为：

    首页固定展示按钮，点击后设置当前功能为 "news-search"

    在用户输入框上方或页面标记当前模式为“新闻搜索模式”

模型提示预设：

    system_prompt: 你是一个新闻信息助手，请基于事实和公开报道简明扼要地回答用户的问题。

    input_prefix: “请从新闻角度回答：”

用户输入示例：

    “深圳近期有哪些新能源投资？”

实际发送内容：

    系统提示词 + “请从新闻角度回答：深圳近期有哪些新能源投资？”

📌 功能二：「帮我写作」
功能目的：

引导用户请求写一段文本、起草内容、润色文字等，例如“帮我写一段项目介绍”、“润色下面这段内容”等。
前端行为：

    首页固定展示按钮，点击后设置当前功能为 "writing-assistant"

    页面展示“写作助手模式”提示（可选）

模型提示预设：

    system_prompt: 你是一名专业写作助手，请根据用户要求撰写高质量文本，注重语言流畅、结构清晰、内容完整。

    input_prefix: “请帮我写作：”

用户输入示例：

    “介绍一下我们公司的新能源项目优势。”

实际发送内容：

    系统提示词 + “请帮我写作：介绍一下我们公司的新能源项目优势。”

🔄 通用行为说明：
项目	说明
按钮是否动态渲染	❌ 否（写死在首页）
是否切换模型	❌ 否（统一调用主模型）
用户是否感知 prompt	❌ 否（前端不暴露，仅行为引导）
模式状态如何显示	✅ 可在输入框 placeholder、顶部模式标识等位置展示当前任务