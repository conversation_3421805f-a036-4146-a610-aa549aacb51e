// 通用函数库

// 检查令牌是否存在
function isLoggedIn() {
    return localStorage.getItem('token') !== null;
}

// 格式化日期
function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric', 
        month: 'numeric', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 截断文本
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// 显示错误消息
function showError(message, elementId = 'errorMessage') {
    const errorElement = document.getElementById(elementId);
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.remove('d-none');
    } else {
        console.error(message);
    }
}

// 隐藏错误消息
function hideError(elementId = 'errorMessage') {
    const errorElement = document.getElementById(elementId);
    if (errorElement) {
        errorElement.textContent = '';
        errorElement.classList.add('d-none');
    }
}

// 设置应用程序标题
function setAppTitle(title) {
    document.title = title ? `${title} - 深圳能源AI助手` : '深圳能源AI助手';
}

// 处理API响应错误
function handleApiError(error) {
    console.error('API错误:', error);
    
    if (error.response) {
        // 处理响应错误
        if (error.response.status === 401) {
            // 未授权，重定向到登录页面
            localStorage.removeItem('token');
            window.location.href = '/login';
            return '认证失败，请重新登录';
        }
        
        // 返回错误信息
        return error.response.data?.detail || `错误 ${error.response.status}: 请求失败`;
    } else if (error.request) {
        // 请求发送但没有收到响应
        return '服务器无响应，请稍后再试';
    } else {
        // 请求设置时出错
        return '请求错误: ' + error.message;
    }
}

// 页面访问埋点
async function trackPageVisit(page) {
    // 检查是否已登录
    if (!isLoggedIn()) return;

    try {
        // 发送页面访问埋点请求
        await axios.post('/api/v1/track/visit', {
            page: page
        });
        
        console.log(`页面访问埋点成功: ${page}`);
    } catch (error) {
        console.error('页面访问埋点失败', error);
    }
}

// 初始化页面访问埋点
function initPageTracking() {
    // 获取当前页面路径
    const path = window.location.pathname;
    
    // 跳过登录页面
    if (path === '/login') return;
    
    // 提取页面名称
    let page = 'unknown';
    if (path === '/chat') {
        page = 'chat';
    } else if (path === '/dashboard') {
        page = 'dashboard';
    } else if (path) {
        // 移除首尾斜杠，作为页面名称
        page = path.replace(/^\/|\/$/g, '') || 'home';
    }
    
    // 执行页面访问埋点
    trackPageVisit(page);
}

// 在页面加载完成后自动执行埋点
document.addEventListener('DOMContentLoaded', function() {
    // 如果已登录，执行页面访问埋点
    if (isLoggedIn()) {
        initPageTracking();
    }
});

// 添加axios全局响应拦截器，自动处理401错误
if (typeof axios !== 'undefined') {
    axios.interceptors.response.use(
        response => response, // 正常响应直接返回
        error => {
            if (error.response && error.response.status === 401) {
                console.warn('用户登录已过期或未登录，重定向到登录页面');
                // 清除本地存储的token
                localStorage.removeItem('token');
                // 仅当不在登录页面时才重定向
                if (window.location.pathname !== '/login') {
                    window.location.href = '/login';
                }
            }
            return Promise.reject(error); // 继续抛出错误，让后续代码处理
        }
    );
}

// 添加axios全局请求拦截器，自动添加认证头
if (typeof axios !== 'undefined') {
    axios.interceptors.request.use(
        config => {
            // 获取token
            const token = localStorage.getItem('token');
            // 如果存在token，添加到Authorization头
            if (token) {
                config.headers['Authorization'] = `Bearer ${token}`;
            }
            return config;
        },
        error => {
            return Promise.reject(error);
        }
    );
} 