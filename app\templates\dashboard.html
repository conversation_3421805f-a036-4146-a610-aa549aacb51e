{% extends "base.html" %}

{% block title %}统计看板 - 深圳能源AI助手{% endblock %}

{% block extra_css %}
<style>
    /* 统计看板样式 */
    body {
        background-color: #f8f9fa;
    }
    
    .dashboard-container {
        padding: 2rem;
    }
    
    .stat-card {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        margin-bottom: 2rem;
        transition: transform 0.3s;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .stat-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin: 1rem 0;
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .bg-blue {
        background-color: #4e73df;
    }
    
    .bg-green {
        background-color: #1cc88a;
    }
    
    .bg-orange {
        background-color: #f6c23e;
    }
    
    .bg-red {
        background-color: #e74a3b;
    }
    
    /* 顶部导航栏 */
    .navbar {
        background-color: white;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    
    /* 图表容器 */
    .chart-container {
        position: relative;
        margin: auto;
        height: 350px;
        width: 100%;
    }
    
    .date-filter {
        margin-bottom: 1.5rem;
    }
    
    /* 小仪表板卡片 */
    .mini-stat {
        text-align: center;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }
    
    .mini-stat-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }
    
    .mini-stat-value {
        font-size: 1.5rem;
        font-weight: 700;
    }
    
    .mini-stat-label {
        font-size: 0.85rem;
    }
    
    /* 加载指示器 */
    .loading {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 100;
    }
    
    /* 在线用户指示器 */
    .online-indicator {
        width: 10px;
        height: 10px;
        background-color: #1cc88a;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid dashboard-container">
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white rounded mb-4">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <img src="/static/images/logo.png" alt="Logo" height="30">
                深圳能源AI助手
            </a>
            <div class="d-flex">
                <div class="me-3">
                    <a href="/chat" class="btn btn-outline-primary">
                        <i class="bi bi-chat"></i> 返回聊天
                    </a>
                </div>
                <div class="dropdown">
                    <button class="btn btn-light dropdown-toggle" type="button" id="userMenu" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-person-circle"></i>
                        <span id="userName">用户</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenu">
                        <li><a class="dropdown-item" href="#" id="logoutBtn"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 统计摘要卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">累计访问</span>
                    <div class="stat-icon bg-blue">
                        <i class="bi bi-eye"></i>
                    </div>
                </div>
                <div class="stat-value" id="totalVisits">-</div>
                <div class="stat-label">今日: <span id="todayVisits">-</span></div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">累计提问</span>
                    <div class="stat-icon bg-green">
                        <i class="bi bi-chat-dots"></i>
                    </div>
                </div>
                <div class="stat-value" id="totalQuestions">-</div>
                <div class="stat-label">今日: <span id="todayQuestions">-</span></div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">注册用户</span>
                    <div class="stat-icon bg-orange">
                        <i class="bi bi-people"></i>
                    </div>
                </div>
                <div class="stat-value" id="totalAccounts">-</div>
                <div class="stat-label">活跃用户: <span id="totalLoggedIn">-</span></div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">当前在线</span>
                    <div class="stat-icon bg-red">
                        <i class="bi bi-person-check"></i>
                    </div>
                </div>
                <div class="stat-value" id="onlineUsers">-</div>
                <div class="stat-label">今日登录: <span id="todayLoggedIn">-</span></div>
            </div>
        </div>
    </div>

    <!-- 趋势图表区域 -->
    <div class="row">
        <div class="col-12">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">趋势统计</span>
                    <div class="date-filter">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm date-range-btn" data-range="7">最近7天</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm date-range-btn" data-range="30">最近30天</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm date-range-btn" data-range="365">最近12个月</button>
                        </div>
                        <select class="form-select form-select-sm d-inline-block ms-2" style="width: auto;" id="groupBySelect">
                            <option value="day">按天</option>
                            <option value="hour">按小时</option>
                            <option value="month">按月</option>
                        </select>
                    </div>
                </div>
                <div class="loading" id="chartLoading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 数据更新时间 -->
    <div class="text-end text-muted mt-3">
        <small>数据更新时间: <span id="updateTime">-</span></small>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 全局变量
        let trendChart = null;
        let currentGroupBy = 'day';
        let currentDays = 7;
        
        // 验证登录状态
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '/login';
            return;
        }
        
        // 配置axios默认请求头
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        
        // 获取用户信息
        async function getUserInfo() {
            try {
                const response = await axios.get('/api/v1/auth/userinfo');
                if (response.data.code === 200) {
                    document.getElementById('userName').textContent = response.data.data.username;
                }
            } catch (error) {
                console.error('获取用户信息失败', error);
                if (error.response && error.response.status === 401) {
                    localStorage.removeItem('token');
                    window.location.href = '/login';
                }
            }
        }
        
        // 获取统计汇总数据
        async function getSummaryData() {
            try {
                const response = await axios.get('/api/v1/statistics/summary');
                if (response.data.code === 200) {
                    const data = response.data.data;
                    
                    // 更新访问统计
                    document.getElementById('totalVisits').textContent = data.visits.total.toLocaleString();
                    document.getElementById('todayVisits').textContent = data.visits.today.toLocaleString();
                    
                    // 更新提问统计
                    document.getElementById('totalQuestions').textContent = data.questions.total.toLocaleString();
                    document.getElementById('todayQuestions').textContent = data.questions.today.toLocaleString();
                    
                    // 更新用户统计
                    document.getElementById('totalAccounts').textContent = data.users.total_accounts.toLocaleString();
                    document.getElementById('totalLoggedIn').textContent = data.users.total_logged_in.toLocaleString();
                    document.getElementById('onlineUsers').textContent = data.users.online.toLocaleString();
                    document.getElementById('todayLoggedIn').textContent = data.users.today_logged_in.toLocaleString();
                    
                    // 更新数据时间
                    document.getElementById('updateTime').textContent = data.stat_date + ' ' + new Date().toLocaleTimeString();
                }
            } catch (error) {
                console.error('获取统计汇总数据失败', error);
            }
        }
        
        // 获取趋势数据并绘制图表
        async function getTrendData() {
            // 显示加载指示器
            document.getElementById('chartLoading').style.display = 'block';
            
            try {
                const today = new Date();
                const startDate = new Date();
                startDate.setDate(today.getDate() - currentDays);
                
                const startDateStr = startDate.toISOString().split('T')[0];
                const endDateStr = today.toISOString().split('T')[0];
                
                const response = await axios.get(`/api/v1/statistics/trend?group_by=${currentGroupBy}&start_date=${startDateStr}&end_date=${endDateStr}`);
                
                if (response.data.code === 200) {
                    const trendData = response.data.data;
                    renderChart(trendData);
                }
            } catch (error) {
                console.error('获取趋势数据失败', error);
            } finally {
                // 隐藏加载指示器
                document.getElementById('chartLoading').style.display = 'none';
            }
        }
        
        // 渲染图表
        function renderChart(trendData) {
            const ctx = document.getElementById('trendChart').getContext('2d');
            
            // 准备数据
            const times = trendData.map(item => item.time);
            const visits = trendData.map(item => item.visits || 0);
            const questions = trendData.map(item => item.questions);
            
            // 如果图表已存在，先销毁
            if (trendChart) {
                trendChart.destroy();
            }
            
            // 创建新图表
            trendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: times,
                    datasets: [
                        {
                            label: '访问次数',
                            data: visits,
                            borderColor: '#4e73df',
                            backgroundColor: 'rgba(78, 115, 223, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: '提问次数',
                            data: questions,
                            borderColor: '#1cc88a',
                            backgroundColor: 'rgba(28, 200, 138, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
        }
        
        // 退出登录按钮事件
        document.getElementById('logoutBtn').addEventListener('click', function() {
            localStorage.removeItem('token');
            window.location.href = '/login';
        });
        
        // 绑定日期范围按钮点击事件
        document.querySelectorAll('.date-range-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const days = parseInt(this.dataset.range);
                
                // 更新选中状态
                document.querySelectorAll('.date-range-btn').forEach(b => {
                    b.classList.remove('active', 'btn-secondary');
                    b.classList.add('btn-outline-secondary');
                });
                this.classList.remove('btn-outline-secondary');
                this.classList.add('active', 'btn-secondary');
                
                // 更新当前选择的天数
                currentDays = days;
                
                // 重新获取数据
                getTrendData();
            });
        });
        
        // 绑定分组方式选择事件
        document.getElementById('groupBySelect').addEventListener('change', function() {
            currentGroupBy = this.value;
            
            // 如果选择了按月分组，自动调整时间范围为12个月
            if (currentGroupBy === 'month') {
                currentDays = 365; // 大约一年
                
                // 更新按钮选中状态
                document.querySelectorAll('.date-range-btn').forEach(b => {
                    b.classList.remove('active', 'btn-secondary');
                    b.classList.add('btn-outline-secondary');
                });
                
                // 选中最近12个月按钮
                const yearBtn = document.querySelector('.date-range-btn[data-range="365"]');
                if (yearBtn) {
                    yearBtn.classList.remove('btn-outline-secondary');
                    yearBtn.classList.add('active', 'btn-secondary');
                }
            }
            
            getTrendData();
        });
        
        // 初始化函数
        async function init() {
            // 加载用户信息
            await getUserInfo();
            
            // 记录页面访问
            trackPageVisit('dashboard');
            
            // 获取统计数据
            getSummaryData();
            
            // 获取趋势数据
            getTrendData();
            
            // 设置默认选中的日期范围
            document.querySelector('.date-range-btn[data-range="7"]').classList.add('active', 'btn-secondary');
            document.querySelector('.date-range-btn[data-range="7"]').classList.remove('btn-outline-secondary');
        }
        
        // 启动初始化
        init();
    });
</script>
{% endblock %} 