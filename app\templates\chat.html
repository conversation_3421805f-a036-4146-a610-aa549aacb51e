{% extends "base.html" %}

{% block title %}深圳能源AI助手{% endblock %}

{% block extra_css %}
<style>
    body, html {
        height: 100%;
        overflow: hidden;
    }
    .container-fluid, .row {
        height: 100%;
    }
    /* 侧边栏样式 */
    .sidebar {
        height: 100%;
        background-color: #f8f9fa;
        border-right: 1px solid #dee2e6;
        display: flex;
        flex-direction: column;
    }
    .sidebar-header {
        padding: 15px;
        border-bottom: 1px solid #dee2e6;
    }
    .sidebar-content {
        flex: 1;
        overflow-y: auto;
        padding: 15px;
    }
    .sidebar-footer {
        padding: 15px;
        border-top: 1px solid #dee2e6;
    }
    /* 对话列表样式 */
    .conversation-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    .conversation-item {
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 5px;
        background-color: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        border: 1px solid transparent;
        transition: background-color 0.2s, border-color 0.2s;
    }
    .conversation-item:hover {
        background-color: #e9ecef;
        border-color: #dee2e6;
    }
    .conversation-item.active {
        background-color: #e2f0fd;
        border-color: #90c8f8;
    }
    .conversation-title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        cursor: pointer;
        padding: 5px;
    }
    /* 对话操作按钮 */
    .conversation-actions {
        display: flex;
        visibility: visible;
        opacity: 1;
        margin-left: 8px;
        z-index: 100;
    }
    .conversation-actions .btn {
        width: 32px;
        height: 32px;
        padding: 0;
        margin: 0 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        background-color: transparent;
        border-radius: 4px;
        border: 1px solid transparent;
        cursor: pointer;
    }
    .conversation-actions .btn:hover {
        color: #007bff;
        background-color: rgba(0, 123, 255, 0.1);
        border-color: rgba(0, 123, 255, 0.2);
    }
    .conversation-actions .btn.delete-btn {
        color: #dc3545;
    }
    .conversation-actions .btn.delete-btn:hover {
        background-color: rgba(220, 53, 69, 0.1);
        border-color: rgba(220, 53, 69, 0.2);
    }
    .conversation-actions .btn i {
        font-size: 14px;
    }
    /* 主内容区域样式 */
    .main-content {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .chat-header {
        padding: 15px;
        border-bottom: 1px solid #dee2e6;
    }
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 15px;
        display: flex;
        flex-direction: column;
    }
    .chat-footer {
        padding: 15px;
        border-top: 1px solid #dee2e6;
    }
    /* 消息样式 */
    .message {
        max-width: 80%;
        margin-bottom: 15px;
        position: relative;
    }
    .message-user {
        align-self: flex-end;
        background-color: #d1e7ff;
        padding: 10px 15px;
        border-radius: 10px;
    }
    .message-assistant {
        align-self: flex-start;
        display: flex;
    }
    .message-content {
        word-wrap: break-word;
    }
    /* 消息操作按钮 */
    .message-actions {
        display: flex;
        flex-direction: row;
        gap: 8px;
        margin-top: 8px;
        justify-content: flex-end;
    }
    .message-action-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: none;
        background: #f4f4f4;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        transition: background 0.2s, color 0.2s;
    }
    .message-action-btn:hover {
        background: #e2e8f0;
        color: #007bff;
    }
    .message-action-btn.delete-btn:hover {
        color: #dc3545;
    }
    /* 日期分隔线 */
    .date-divider {
        text-align: center;
        margin: 10px 0;
        position: relative;
    }
    .date-divider::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        width: 100%;
        height: 1px;
        background-color: #dee2e6;
    }
    .date-text {
        position: relative;
        background-color: white;
        padding: 0 10px;
        font-size: 0.8rem;
        color: #6c757d;
    }
    /* 输入指示器 */
    .typing-indicator {
        display: inline-block;
        position: relative;
        width: 40px;
        height: 20px;
    }
    .typing-indicator span {
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: #6c757d;
        border-radius: 50%;
        margin: 0 2px;
        animation: bounce 1.5s infinite ease-in-out;
    }
    .typing-indicator span:nth-child(2) {
        animation-delay: 0.2s;
    }
    .typing-indicator span:nth-child(3) {
        animation-delay: 0.4s;
    }
    @keyframes bounce {
        0%, 60%, 100% { transform: translateY(0); }
        30% { transform: translateY(-5px); }
    }
    /* 头像和消息气泡 */
    .avatar-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 10px;
    }
    .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-bottom: 3px;
    }
    .avatar-name {
        font-size: 12px;
        color: #666;
        text-align: center;
        white-space: nowrap;
    }
    .message-bubble {
        background-color: #f1f1f1;
        padding: 10px 15px;
        border-radius: 10px;
        position: relative;
    }
    .response-time {
        font-size: 12px;
        color: #999;
        margin-top: 5px;
        text-align: right;
    }
    /* 联网搜索相关样式 */
    .searched-links {
        margin-top: 5px;
        display: flex;
        gap: 8px;
    }
    .searched-link {
        font-size: 12px;
        color: #007bff;
        text-decoration: underline;
        cursor: pointer;
    }
    /* 文件上传相关样式 */
    .file-info {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }
    .file-badge {
        display: inline-block;
        background-color: #e9ecef;
        padding: 2px 8px;
        border-radius: 12px;
        margin-right: 5px;
        margin-bottom: 5px;
        font-size: 12px;
    }
    /* 聊天选项 */
    .chat-options {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-top: 1px solid #dee2e6;
        margin-top: 5px;
    }
    /* 反馈按钮样式 */
    .message-feedback {
        display: flex;
        flex-direction: row;
        gap: 8px;
        margin-top: 8px;
        justify-content: flex-end;
    }
    .feedback-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: none;
        background: #f4f4f4;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        transition: background 0.2s, color 0.2s;
    }
    .feedback-btn:hover {
        background: #e2e8f0;
        color: #007bff;
    }
    .feedback-btn.active {
        background: #d1e7dd;
        color: #198754;
    }
    .feedback-btn.dislike.active {
        background: #f8d7da;
        color: #dc3545;
    }
    .feedback-btn.regenerate {
        color: #0d6efd;
        background: #f4f8ff;
    }
    .feedback-btn.regenerate:hover {
        background: #e7f1ff;
        color: #0a58ca;
    }
    /* 消息状态样式 */
    .message-status {
        font-size: 12px;
        color: #6c757d;
        margin-left: 5px;
    }
    .message-status.edited {
        color: #0d6efd;
    }
    .message-status.deleted {
        color: #dc3545;
    }
    /* 等待重新生成状态 */
    .waiting-regenerate {
        opacity: 0.6;
        position: relative;
    }
    .waiting-regenerate::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        z-index: 1;
    }
    /* 消息编辑样式 */
    .message-editing {
        position: relative;
        margin-top: 10px;
    }
    .message-editing textarea {
        width: 100%;
        min-height: 60px;
        padding: 8px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        resize: vertical;
    }
    .message-editing-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 5px;
    }
    /* 思考过程样式 */
    .thinking-process {
        margin-top: 10px;
        border-left: 3px solid #a0c5e8;
        padding-left: 10px;
        font-size: 0.9rem;
        color: #666;
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        white-space: pre-wrap;
        display: none; /* 默认隐藏 */
    }
    
    .thinking-toggle {
        margin-top: 5px;
        font-size: 0.8rem;
        color: #007bff;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
    }
    
    .thinking-toggle i {
        margin-right: 5px;
        transition: transform 0.3s;
    }
    
    .thinking-toggle.expanded i {
        transform: rotate(90deg);
    }
    
    /* 移除AI消息气泡中的复制按钮样式 */
    .message-bubble {
        position: relative;
    }
    
    /* 修改反馈按钮中的复制按钮样式 */
    .feedback-btn.copy {
        color: #0d6efd;
        background: #f4f8ff;
    }
    
    .feedback-btn.copy:hover {
        background: #e7f1ff;
        color: #0a58ca;
    }
</style>
{% endblock %}

{% block content %}
<!-- 左侧导航栏 -->
<div class="col-md-3 col-lg-2 sidebar">
    <div class="sidebar-header">
        <h5 class="mb-0">深圳能源</h5>
    </div>
    <div class="sidebar-content">
        <button class="btn btn-primary w-100 mb-3" id="newChatBtn">
            <i class="bi bi-plus-lg"></i> 开启新对话
        </button>
        
        <div class="date-divider mb-3">
            <span class="date-text">今天</span>
        </div>
        
        <ul class="conversation-list" id="conversationList">
            <!-- 对话列表将通过JavaScript动态生成 -->
        </ul>
    </div>
    <div class="sidebar-footer">
        <div class="d-flex align-items-center">
            <i class="bi bi-person-circle fs-5 me-2"></i>
            <span id="userInfo">admin1</span>
            <button class="btn btn-sm btn-link ms-auto" id="logoutBtn">
                <i class="bi bi-box-arrow-right"></i>
            </button>
        </div>
        <div class="mt-2">
            <a href="/dashboard" class="btn btn-sm btn-outline-secondary w-100">
                <i class="bi bi-bar-chart-line"></i> 统计看板
            </a>
        </div>
    </div>
</div>

<!-- 右侧主内容区 -->
<div class="col-md-9 col-lg-10 main-content">
    <div class="chat-header">
        <h4 id="chatTitle">新对话</h4>
    </div>
    <div class="chat-messages" id="chatMessages">
        <!-- 消息将通过JavaScript动态生成 -->
    </div>
    <div class="chat-footer">
        <form id="messageForm">
            <div class="input-group">
                <input type="text" class="form-control" id="messageInput" placeholder="输入消息..." autocomplete="off">
                <button type="button" class="btn btn-outline-secondary" id="uploadBtn" title="上传文件">
                    <i class="bi bi-paperclip"></i>
                </button>
                <button class="btn btn-primary" type="submit">
                    <i class="bi bi-send"></i>
                </button>
            </div>
            
            <!-- 文件上传 -->
            <input type="file" id="fileInput" style="display: none;">
            <div id="fileInfo" class="file-info mt-2" style="display: none;"></div>
            
            <!-- 选项区域 -->
            <div class="chat-options">
                <div class="d-flex align-items-center">
                    <div class="form-check form-switch me-4">
                        <input class="form-check-input" type="checkbox" id="useWebSwitch">
                        <label class="form-check-label" for="useWebSwitch">启用联网搜索</label>
                    </div>
                    
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="functionDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            选择功能
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="functionDropdown">
                            <li><a class="dropdown-item" href="#" data-function="default">默认对话</a></li>
                            <li><a class="dropdown-item" href="#" data-function="news-search">新闻搜索</a></li>
                            <li><a class="dropdown-item" href="#" data-function="writing-assistant">帮我写作</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 消息模板 -->
<template id="messageTemplate">
    <div class="message message-user">
        <div class="message-content"></div>
        <div class="message-actions">
            <button class="message-action-btn edit-btn" title="编辑">
                <i class="bi bi-pencil"></i>
            </button>
            <button class="message-action-btn copy-btn" title="复制">
                <i class="bi bi-clipboard"></i>
            </button>
        </div>
        <div class="message-status"></div>
    </div>
</template>

<template id="assistantMessageTemplate">
    <div class="message message-assistant">
        <div class="avatar-wrapper">
            <img src="/static/images/ai_avatar.png" alt="AI助手" class="avatar">
            <span class="avatar-name">AI助手</span>
        </div>
        <div class="message-bubble">
            <div class="thinking-toggle">
                <i class="bi bi-chevron-right"></i>
                <span>查看思考过程</span>
            </div>
            <div class="thinking-process"></div>
            <div class="message-content"></div>
        </div>
        <div class="message-feedback">
            <button class="feedback-btn like" title="点赞">
                <i class="bi bi-hand-thumbs-up"></i>
            </button>
            <button class="feedback-btn dislike" title="差评">
                <i class="bi bi-hand-thumbs-down"></i>
            </button>
            <button class="feedback-btn copy" title="复制回答">
                <i class="bi bi-clipboard"></i>
            </button>
            <button class="feedback-btn regenerate" title="重新生成">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
        </div>
    </div>
</template>

<!-- 消息编辑模板 -->
<template id="messageEditTemplate">
    <div class="message-editing">
        <textarea class="form-control"></textarea>
        <div class="message-editing-actions">
            <button class="btn btn-sm btn-secondary cancel-edit">取消</button>
            <button class="btn btn-sm btn-primary save-edit">保存</button>
        </div>
    </div>
</template>

<!-- 在body中添加Toast容器 -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
  <div id="liveToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header">
      <strong class="me-auto" id="toastTitle">通知</strong>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body" id="toastMessage"></div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 格式化消息内容（处理换行等）
    function formatMessage(content) {
        return content.replace(/\n/g, '<br>');
    }

    // 设置思考过程辅助函数
    function setupThinkingProcess(messageElement, thinkingContent) {
        // 如果是用户消息，不显示思考过程
        if (messageElement.classList.contains('message-user')) {
            return;
        }
        
        const thinkingToggle = messageElement.querySelector('.thinking-toggle');
        
        // 确保thinkingToggle元素存在
        if (!thinkingToggle) {
            console.warn('思考过程切换按钮不存在', messageElement);
            return;
        }
        
        if (thinkingContent) {
            const thinkingDiv = messageElement.querySelector('.thinking-process');
            thinkingDiv.textContent = thinkingContent;
            
            thinkingToggle.style.display = 'inline-flex';
            
            // 绑定点击事件
            thinkingToggle.addEventListener('click', function() {
                const thinkingDiv = this.nextElementSibling;
                if (thinkingDiv.style.display === 'none' || !thinkingDiv.style.display) {
                    thinkingDiv.style.display = 'block';
                    this.classList.add('expanded');
                    this.querySelector('span').textContent = '隐藏思考过程';
                } else {
                    thinkingDiv.style.display = 'none';
                    this.classList.remove('expanded');
                    this.querySelector('span').textContent = '查看思考过程';
                }
            });
        } else {
            // 隐藏思考过程按钮
            thinkingToggle.style.display = 'none';
        }
    }
    
    // 添加反馈
    async function addFeedback(messageId, feedbackType) {
        try {
            const response = await fetch(`/api/v1/chat/messages/feedback`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({ 
                    message_id: messageId,
                    feedback_type: feedbackType 
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                if (result.code === 200) {
                    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                    if (messageElement) {
                        const feedbackBtn = messageElement.querySelector(`.feedback-btn.${feedbackType}`);
                        if (feedbackBtn) {
                            feedbackBtn.classList.add('active');
                        }
                    }
                }
            }
        } catch (error) {
            console.error('添加反馈失败:', error);
            showToast('添加反馈失败', 'error');
        }
    }
    
    // 消息反馈按钮绑定
    function addFeedbackActions(messageElement, message) {
        const likeBtn = messageElement.querySelector('.like');
        const dislikeBtn = messageElement.querySelector('.dislike');
        const copyBtn = messageElement.querySelector('.copy');
        const regenerateBtn = messageElement.querySelector('.regenerate');
        
        if (likeBtn) {
            likeBtn.addEventListener('click', () => addFeedback(message.id, 'like'));
        }
        
        if (dislikeBtn) {
            dislikeBtn.addEventListener('click', () => addFeedback(message.id, 'dislike'));
        }
        
        if (copyBtn) {
            copyBtn.addEventListener('click', () => copyMessage(message.content));
        }
        
        if (regenerateBtn) {
            regenerateBtn.addEventListener('click', () => regenerateResponse(message.id));
        }
    }
    
    // 重新生成回答
    async function regenerateResponse(messageId) {
        const originalMessage = document.querySelector(`[data-message-id="${messageId}"]`);
        let chatMessages = document.getElementById('chatMessages');
        
        // 记录是否为用户消息，用于区分编辑问题触发的重新生成
        const isUserMessage = originalMessage && originalMessage.dataset.role === 'user';
        console.log('正在重新生成回答，消息ID:', messageId, '是用户消息:', isUserMessage);
        
        if (originalMessage) {
            // 如果是用户消息，立即应用强显示样式，确保不会被隐藏
            if (isUserMessage) {
                console.log('用户消息预防性应用强制显示样式');
                originalMessage.style.cssText = `
                    display: block !important; 
                    visibility: visible !important; 
                    opacity: 1 !important;
                    position: relative !important;
                    z-index: 100 !important;
                `;
            }
        
            // 添加等待状态
            originalMessage.classList.add('waiting-regenerate');
            
            // 保存原始显示样式
            const originalDisplay = originalMessage.style.display;
            originalMessage.dataset.originalDisplay = originalDisplay || '';
            
            // 获取消息内容元素
            const contentDiv = originalMessage.querySelector('.message-content');
            if (contentDiv) {
                // 保存原始内容
                const originalContent = contentDiv.innerHTML;
                contentDiv.dataset.originalContent = originalContent;
                
                // 如果是AI消息，显示加载动画
                if (!isUserMessage) {
                    contentDiv.innerHTML = `
                        <div class="text-center p-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">正在重新生成...</span>
                            </div>
                            <div class="mt-2 text-muted small">正在重新生成回答...</div>
                        </div>
                    `;
                }
            }
            
            // 隐藏反馈按钮
            const feedbackButtons = originalMessage.querySelector('.message-feedback');
            if (feedbackButtons) {
                feedbackButtons.style.display = 'none';
            }
        }
        
        try {
            const response = await fetch(`/api/v1/chat/messages/regenerate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    message_id: messageId
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                
                if (result.code === 200) {
                    // 获取新生成的消息数据
                    const newMessage = result.data;
                    console.log('重新生成成功，获取到新消息:', newMessage);
                    // 输出思考过程，查看是否正常返回
                    console.log('重新生成消息的思考过程:', newMessage.thinking);
                    
                    // 如果有parent_message_id，说明是替换消息
                    if (newMessage.parent_message_id) {
                        // 查找原始消息并标记为已删除
                        const parentMessageElement = document.querySelector(`[data-message-id="${newMessage.parent_message_id}"]`);
                        if (parentMessageElement) {
                            // 如果是AI助手消息才隐藏，用户消息保持可见
                            if (parentMessageElement.dataset.role !== 'user') {
                                parentMessageElement.style.display = 'none';
                            } else {
                                console.log('保持用户父消息可见:', newMessage.parent_message_id);
                            }
                        }
                    }
                    
                    // 如果是直接调用regenerate，隐藏当前消息，但如果是用户消息则保持可见
                    if (originalMessage) {
                        // 只隐藏AI消息，用户消息保持可见
                        if (originalMessage.dataset.role !== 'user') {
                            console.log('隐藏AI原始消息:', messageId);
                            originalMessage.style.display = 'none';
                        } else {
                            // 如果是用户消息，强制可见
                            console.log('强制用户消息可见:', messageId);
                            
                            // 强制显示用户消息
                            originalMessage.style.cssText = `
                                display: block !important; 
                                visibility: visible !important; 
                                opacity: 1 !important;
                                position: relative !important;
                                z-index: 100 !important;
                            `;
                            originalMessage.classList.remove('waiting-regenerate');
                            
                            // 如果已保存了原始内容，确保恢复
                            const contentDiv = originalMessage.querySelector('.message-content');
                            if (contentDiv && contentDiv.dataset.originalContent) {
                                contentDiv.innerHTML = contentDiv.dataset.originalContent;
                            }
                        }
                    }
                    
                    // 添加新消息
                    const template = document.getElementById('assistantMessageTemplate');
                    const messageElement = template.content.cloneNode(true);
                    const messageContainer = messageElement.querySelector('.message');
                    const contentDiv = messageElement.querySelector('.message-content');
                    
                    // 设置新消息内容
                    contentDiv.innerHTML = formatMessage(newMessage.content || '');
                    messageContainer.setAttribute('data-message-id', newMessage.id);
                    messageContainer.dataset.role = 'assistant';
                    messageContainer.dataset.status = newMessage.status || 'active';
                    
                    // 添加到DOM
                    chatMessages.appendChild(messageElement);
                    
                    // 获取真正添加到DOM的元素
                    const addedMessageElement = chatMessages.querySelector(`[data-message-id="${newMessage.id}"]`);
                    
                    if (addedMessageElement) {
                        // 只为AI消息设置思考过程，用户消息不需要
                        if (newMessage.role !== 'user' && newMessage.thinking) {
                            setupThinkingProcess(addedMessageElement, newMessage.thinking);
                        }
                        
                        // 设置消息状态
                        if (newMessage.status !== 'active') {
                            const statusDiv = addedMessageElement.querySelector('.message-status');
                            if (statusDiv) {
                                statusDiv.textContent = newMessage.status === 'edited' ? '(已编辑)' : '(已删除)';
                                statusDiv.classList.add(newMessage.status);
                            }
                        }
                        
                        // 添加反馈按钮事件
                        addFeedbackActions(addedMessageElement, newMessage);
                    }
                    
                    // 再次检查所有用户消息，确保它们可见
                    document.querySelectorAll('.message[data-role="user"]').forEach(userMsg => {
                        console.log('保证用户消息可见:', userMsg.dataset.messageId);
                        userMsg.style.cssText = `
                            display: block !important; 
                            visibility: visible !important; 
                            opacity: 1 !important;
                        `;
                    });
                    
                    // 强制布局重绘并滚动到底部
                    setTimeout(() => {
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    }, 100);
                    
                    // 显示成功提示
                    showToast("回答已重新生成", "success");
                    return true;
                } else {
                    console.error('重新生成失败:', result);
                    showToast("重新生成回答失败: " + result.message, "error");
                    
                    // 恢复原始消息
                    if (originalMessage) {
                        originalMessage.classList.remove('waiting-regenerate');
                        
                        // 恢复内容
                        const contentDiv = originalMessage.querySelector('.message-content');
                        if (contentDiv && contentDiv.dataset.originalContent) {
                            contentDiv.innerHTML = contentDiv.dataset.originalContent;
                        }
                        
                        // 恢复反馈按钮
                        const feedbackButtons = originalMessage.querySelector('.message-feedback');
                        if (feedbackButtons) {
                            feedbackButtons.style.display = 'flex';
                        }
                    }
                    
                    return false;
                }
            } else {
                // 处理HTTP错误
                console.error('重新生成请求失败:', response.status);
                
                // 恢复原始消息
                if (originalMessage) {
                    originalMessage.classList.remove('waiting-regenerate');
                    
                    // 恢复内容
                    const contentDiv = originalMessage.querySelector('.message-content');
                    if (contentDiv && contentDiv.dataset.originalContent) {
                        contentDiv.innerHTML = contentDiv.dataset.originalContent;
                    }
                    
                    // 恢复反馈按钮
                    const feedbackButtons = originalMessage.querySelector('.message-feedback');
                    if (feedbackButtons) {
                        feedbackButtons.style.display = 'flex';
                    }
                }
                
                showToast("重新生成回答失败，请重试", "error");
                return false;
            }
        } catch (error) {
            console.error('重新生成请求异常:', error);
            
            // 恢复原始消息
            if (originalMessage) {
                originalMessage.classList.remove('waiting-regenerate');
                
                // 恢复内容
                const contentDiv = originalMessage.querySelector('.message-content');
                if (contentDiv && contentDiv.dataset.originalContent) {
                    contentDiv.innerHTML = contentDiv.dataset.originalContent;
                }
                
                // 恢复反馈按钮
                const feedbackButtons = originalMessage.querySelector('.message-feedback');
                if (feedbackButtons) {
                    feedbackButtons.style.display = 'flex';
                }
            }
            
            showToast("重新生成回答失败，网络异常", "error");
            return false;
        }
    }
    
    // 显示Toast消息提示
    function showToast(msg, type) {
        // 设置Toast类型和内容
        const toast = document.getElementById('liveToast');
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');
        
        // 设置标题和消息
        toastTitle.textContent = type === 'error' ? '错误' : 
                               type === 'warning' ? '警告' : 
                               type === 'info' ? '提示' : '成功';
        toastMessage.textContent = msg;
        
        // 设置样式
        toast.classList.remove('bg-danger', 'text-white', 'bg-success', 'bg-warning', 'bg-info');
        if (type === 'error') {
            toast.classList.add('bg-danger', 'text-white');
        } else if (type === 'success') {
            toast.classList.add('bg-success', 'text-white');
        } else if (type === 'warning') {
            toast.classList.add('bg-warning');
        } else if (type === 'info') {
            toast.classList.add('bg-info', 'text-white');
        }
        
        // 显示Toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
    
    document.addEventListener('DOMContentLoaded', function() {
        // 检查是否已登录
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '/login';
            return;
        }
        
        // 设置axios默认头
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        
        // 全局变量
        let currentConversationId = null;
        let currentDocumentId = null;
        let currentFilePath = null;
        let currentFunction = 'default'; // 初始为默认对话功能
        
        // 获取DOM元素
        const conversationList = document.getElementById('conversationList');
        const chatMessages = document.getElementById('chatMessages');
        const messageForm = document.getElementById('messageForm');
        const messageInput = document.getElementById('messageInput');
        const chatTitle = document.getElementById('chatTitle');
        const newChatBtn = document.getElementById('newChatBtn');
        const logoutBtn = document.getElementById('logoutBtn');
        const uploadBtn = document.getElementById('uploadBtn');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const useWebSwitch = document.getElementById('useWebSwitch');
        const functionDropdown = document.getElementById('functionDropdown');
        
        // 文件上传相关事件
        uploadBtn.addEventListener('click', function() {
            fileInput.click();
        });
        
        fileInput.addEventListener('change', function(e) {
            if (fileInput.files.length > 0) {
                const file = fileInput.files[0];
                uploadFile(file);
            }
        });
        
        // 上传文件
        async function uploadFile(file) {
            // 显示上传中状态
            fileInfo.textContent = `正在上传: ${file.name}...`;
            fileInfo.style.display = 'block';
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                
                const response = await axios.post('/api/v1/chat/upload', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });
                
                // 初始化文件路径数组（如果不存在）
                if (!window.uploadedFiles) {
                    window.uploadedFiles = [];
                }
                
                // 保存文档信息到数组
                window.uploadedFiles.push({
                    id: response.data.data.id,
                    path: response.data.data.file_path,
                    filename: response.data.data.filename
                });
                
                // 更新文件显示
                updateFileDisplay();
            } catch (error) {
                fileInfo.textContent = `上传失败: ${error.response?.data?.message || '未知错误'}`;
                setTimeout(() => {
                    fileInfo.style.display = 'none';
                    fileInput.value = '';
                }, 3000);
            }
        }
        
        // 更新文件显示
        function updateFileDisplay() {
            if (!window.uploadedFiles || window.uploadedFiles.length === 0) {
                fileInfo.style.display = 'none';
                return;
            }
            
            fileInfo.style.display = 'block';
            fileInfo.innerHTML = '';
            
            // 为每个文件创建一个标签
            window.uploadedFiles.forEach((file, index) => {
                const fileTag = document.createElement('span');
                fileTag.className = 'file-badge';
                fileTag.innerHTML = `
                    <i class="bi bi-file-earmark"></i> ${file.filename}
                    <button type="button" class="btn-close btn-close-white ms-2" style="font-size: 0.5rem;" data-index="${index}"></button>
                `;
                fileInfo.appendChild(fileTag);
                
                // 为删除按钮添加事件
                const removeBtn = fileTag.querySelector('.btn-close');
                removeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const fileIndex = this.getAttribute('data-index');
                    window.uploadedFiles.splice(fileIndex, 1);
                    updateFileDisplay();
                });
            });
        }
        
        // 获取所有已上传文件的路径
        function getUploadedFilePaths() {
            if (!window.uploadedFiles || window.uploadedFiles.length === 0) {
                return null;
            }
            return window.uploadedFiles.map(file => file.path);
        }
        
        // 清除所有上传的文件
        function clearUploadedFiles() {
            window.uploadedFiles = [];
            fileInfo.style.display = 'none';
            fileInput.value = '';
        }
        
        // 获取用户信息
        async function getUserInfo() {
            try {
                const response = await axios.get('/api/v1/auth/me');
                document.getElementById('userInfo').textContent = response.data.data.username;
            } catch (error) {
                console.error('获取用户信息失败', error);
                if (error.response && error.response.status === 401) {
                    // 令牌无效，重定向到登录页面
                    localStorage.removeItem('token');
                    window.location.href = '/login';
                }
            }
        }
        
        // 加载对话列表
        async function loadConversations() {
            try {
                const response = await axios.get('/api/v1/chat/conversations');
                const conversations = response.data.data;
                
                // 清空列表
                conversationList.innerHTML = '';
                
                // 添加对话列表项
                conversations.forEach(conv => {
                    const listItem = createConversationItem(conv);
                    conversationList.appendChild(listItem);
                });
                
                // 如果有对话，默认选择第一个
                if (conversations.length > 0 && !currentConversationId) {
                    selectConversation(conversations[0].id);
                }
            } catch (error) {
                console.error('加载对话列表失败', error);
            }
        }
        
        // 创建对话列表项
        function createConversationItem(conversation) {
            const listItem = document.createElement('li');
            listItem.className = 'conversation-item';
            listItem.dataset.id = conversation.id;
            if (currentConversationId === conversation.id) {
                listItem.classList.add('active');
            }
            
            // 创建标题元素
            const titleDiv = document.createElement('div');
            titleDiv.className = 'conversation-title';
            titleDiv.textContent = conversation.title;
            titleDiv.onclick = function() {
                selectConversation(conversation.id);
            };
            
            // 创建操作按钮容器
            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'conversation-actions';
            
            // 创建重命名按钮
            const renameBtn = document.createElement('button');
            renameBtn.className = 'btn btn-sm rename-btn';
            renameBtn.type = 'button';
            renameBtn.innerHTML = '<i class="bi bi-pencil"></i>';
            renameBtn.title = '重命名此对话';

            // 绑定重命名事件
            renameBtn.onclick = function(e) {
                e.stopPropagation();
                e.preventDefault();
                
                const newTitle = prompt('请输入新的对话标题:', conversation.title);
                if (newTitle && newTitle !== conversation.title) {
                    // 显示重命名中状态
                    showToast('正在重命名对话...', 'info');
                    
                    // 使用XMLHttpRequest发送请求
                    const xhr = new XMLHttpRequest();
                    xhr.open('PUT', `/api/v1/chat/conversations/update`, true);
                    
                    // 添加headers
                    xhr.setRequestHeader('Authorization', `Bearer ${localStorage.getItem('token')}`);
                    xhr.setRequestHeader('Content-Type', 'application/json');
                    
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            try {
                                const data = JSON.parse(xhr.responseText);
                                
                                if (xhr.status === 200 && data.code === 200) {
                                    // 更新DOM中的标题
                                    const item = document.querySelector(`.conversation-item[data-id="${conversation.id}"]`);
                                    if (item) {
                                        const titleElement = item.querySelector('.conversation-title');
                                        if (titleElement) {
                                            titleElement.textContent = newTitle;
                                            showToast('对话已重命名', 'success');
                                        }
                                        
                                        // 如果是当前对话，更新标题
                                        if (currentConversationId === conversation.id) {
                                            chatTitle.textContent = newTitle;
                                        }
                                    } else {
                                        showToast('重命名成功，但页面需要刷新', 'warning');
                                    }
                                } else {
                                    showToast(`重命名失败: ${data.message || '请重试'}`, 'error');
                                }
                            } catch (parseError) {
                                showToast('重命名请求异常，请重试', 'error');
                            }
                        }
                    };
                    
                    xhr.onerror = function() {
                        showToast('网络错误，请检查连接后重试', 'error');
                    };
                    
                    // 准备数据并发送请求
                    const requestData = JSON.stringify({ 
                        conversation_id: conversation.id,
                        title: newTitle 
                    });
                    xhr.send(requestData);
                }
                
                return false;
            };
            
            // 创建删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn btn-sm delete-btn';
            deleteBtn.type = 'button';
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
            deleteBtn.title = '删除此对话';

            // 绑定删除事件
            deleteBtn.onclick = function(e) {
                e.stopPropagation();
                e.preventDefault();
                
                if (confirm(`确定要删除"${conversation.title}"对话吗？`)) {
                    // 显示删除中状态
                    showToast('正在删除对话...', 'info');
                    
                    // 使用XMLHttpRequest发送请求
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', `/api/v1/chat/conversations/delete`, true);
                    
                    // 添加headers
                    xhr.setRequestHeader('Authorization', `Bearer ${localStorage.getItem('token')}`);
                    xhr.setRequestHeader('Content-Type', 'application/json');
                    
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            try {
                                const data = JSON.parse(xhr.responseText);
                                
                                if (xhr.status === 200 && data.code === 200) {
                                    // 直接从DOM中移除
                                    const item = document.querySelector(`.conversation-item[data-id="${conversation.id}"]`);
                                    if (item) {
                                        item.remove();
                                        showToast('对话已删除', 'success');
                                        
                                        // 如果删除的是当前对话，选择第一个或创建新对话
                                        if (currentConversationId === conversation.id) {
                                            const firstConversation = document.querySelector('.conversation-item');
                                            if (firstConversation) {
                                                selectConversation(firstConversation.dataset.id);
                                            } else {
                                                createNewConversation();
                                            }
                                        }
                                    } else {
                                        showToast('对话已删除，但页面需要刷新', 'warning');
                                        setTimeout(() => {
                                            window.location.reload();
                                        }, 1500);
                                    }
                                } else {
                                    showToast(`删除失败: ${data.message || '请重试'}`, 'error');
                                }
                            } catch (parseError) {
                                showToast('删除请求异常，请重试', 'error');
                            }
                        }
                    };
                    
                    xhr.onerror = function() {
                        showToast('网络错误，请检查连接后重试', 'error');
                    };
                    
                    // 准备数据并发送请求
                    const requestData = JSON.stringify({ conversation_id: conversation.id });
                    xhr.send(requestData);
                }
                
                return false;
            };
            
            // 添加按钮到操作容器
            actionsDiv.appendChild(renameBtn);
            actionsDiv.appendChild(deleteBtn);
            
            // 添加元素到列表项
            listItem.appendChild(titleDiv);
            listItem.appendChild(actionsDiv);
            
            return listItem;
        }
        
        // 选择对话
        async function selectConversation(conversationId) {
            // 更新选中状态
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            
            const selectedItem = document.querySelector(`.conversation-item[data-id="${conversationId}"]`);
            if (selectedItem) {
                selectedItem.classList.add('active');
            }
            
            currentConversationId = conversationId;
            
            // 加载对话消息
            try {
                const response = await axios.post(`/api/v1/chat/conversations/detail`, {
                    conversation_id: conversationId
                });
                const conversation = response.data.data;
                
                // 设置标题
                chatTitle.textContent = conversation.title;
                
                // 显示消息
                displayMessages(conversation.messages);
            } catch (error) {
                console.error('加载对话消息失败', error);
            }
        }
        
        // 显示消息
        function displayMessages(messages) {
            chatMessages.innerHTML = '';
            
            if (messages.length === 0) {
                // 显示欢迎消息
                const welcomeDiv = document.createElement('div');
                welcomeDiv.className = 'message message-assistant';
                welcomeDiv.innerHTML = `
                    <div class="avatar-wrapper">
                        <img src="/static/images/ai_avatar.png" alt="AI" class="avatar">
                        <div class="avatar-name">AI助手</div>
                    </div>
                    <div>
                        <div class="message-bubble">下午好！今天过得怎么样？有什么我可以为您效劳的吗？</div>
                        <div class="response-time">响应时间：${new Date().toLocaleTimeString()}</div>
                    </div>
                `;
                chatMessages.appendChild(welcomeDiv);
                return;
            }
            
            // 显示所有消息
            messages.forEach(message => {
                if (message.role === 'user') {
                    // 使用用户消息模板
                    const template = document.getElementById('messageTemplate');
                    const messageElement = template.content.cloneNode(true);
                    const messageContainer = messageElement.querySelector('.message');
                    messageContainer.setAttribute('data-message-id', message.id);
                    messageContainer.dataset.role = 'user';
                    
                    // 设置消息内容
                    const contentDiv = messageElement.querySelector('.message-content');
                    contentDiv.innerHTML = formatMessage(message.content);
                    
                    // 添加到DOM
                    chatMessages.appendChild(messageElement);
                    
                    // 获取真正添加到DOM的元素
                    const addedMessageElement = chatMessages.querySelector(`[data-message-id="${message.id}"]`);
                    
                    if (addedMessageElement) {
                        // 设置消息状态
                        if (message.status !== 'active') {
                            const statusDiv = addedMessageElement.querySelector('.message-status');
                            if (statusDiv) {
                                statusDiv.textContent = message.status === 'edited' ? '(已编辑)' : '(已删除)';
                                statusDiv.classList.add(message.status);
                            }
                        }
                        
                        // 添加操作按钮事件
                        const editBtn = addedMessageElement.querySelector('.edit-btn');
                        const copyBtn = addedMessageElement.querySelector('.copy-btn');
                        
                        if (editBtn) {
                            editBtn.addEventListener('click', () => editMessage(message));
                        }
                        
                        if (copyBtn) {
                            copyBtn.addEventListener('click', () => copyMessage(message.content));
                        }
                    }
                } else {
                    // 使用AI助手消息模板
                    const template = document.getElementById('assistantMessageTemplate');
                    const messageElement = template.content.cloneNode(true);
                    const messageContainer = messageElement.querySelector('.message');
                    messageContainer.setAttribute('data-message-id', message.id);
                    messageContainer.dataset.role = 'assistant';
                    
                    // 设置消息内容
                    const contentDiv = messageElement.querySelector('.message-content');
                    contentDiv.innerHTML = formatMessage(message.content);
                    
                    // 添加到DOM
                    chatMessages.appendChild(messageElement);
                    
                    // 获取真正添加到DOM的元素
                    const addedMessageElement = chatMessages.querySelector(`[data-message-id="${message.id}"]`);
                    
                    if (addedMessageElement) {
                        // 设置思考过程(如果有)
                        setupThinkingProcess(addedMessageElement, message.thinking);
                        
                        // 添加反馈按钮事件
                        addFeedbackActions(addedMessageElement, message);
                    }
                }
            });
            
            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 创建新对话
        async function createNewConversation() {
            try {
                const response = await axios.post('/api/v1/chat/conversations', {
                    title: '新对话'
                });
                
                // 添加到列表并选择
                const newConversation = response.data.data;
                const listItem = createConversationItem(newConversation);
                conversationList.insertBefore(listItem, conversationList.firstChild);
                
                selectConversation(newConversation.id);
            } catch (error) {
                console.error('创建新对话失败', error);
            }
        }
        
        // 发送消息
        async function sendMessage(content) {
            // 如果没有选中的对话，创建一个
            if (!currentConversationId) {
                await createNewConversation();
            }
            
            // 根据当前功能处理输入内容
            let finalContent = content;
            let systemPrompt = "";
            
            if (currentFunction === 'news-search') {
                systemPrompt = "你是一个新闻信息助手，请基于事实和公开报道简明扼要地回答用户的问题。";
                finalContent = "请从新闻角度回答：" + content;
            } else if (currentFunction === 'writing-assistant') {
                systemPrompt = "你是一名专业写作助手，请根据用户要求撰写高质量文本，注重语言流畅、结构清晰、内容完整。";
                finalContent = "请帮我写作：" + content;
            }
            
            // 显示用户消息
            const template = document.getElementById('messageTemplate');
            const messageElement = template.content.cloneNode(true);
            const messageContainer = messageElement.querySelector('.message');
            const contentDiv = messageElement.querySelector('.message-content');
            contentDiv.innerHTML = formatMessage(content); // 显示原始内容，而不是修改后的
            
            // 确保操作按钮可见并添加事件监听
            const editBtn = messageElement.querySelector('.edit-btn');
            const copyBtn = messageElement.querySelector('.copy-btn');
            
            // 给临时消息指定一个临时ID，后续会被更新
            const tempMessageId = 'temp_' + Date.now();
            messageContainer.setAttribute('data-message-id', tempMessageId);
            messageContainer.dataset.role = 'user';
            
            // 添加事件监听
            if (editBtn) {
                editBtn.addEventListener('click', function() {
                    // 获取更新后的消息ID
                    const currentMsgId = messageContainer.getAttribute('data-message-id');
                    editMessage({
                        id: currentMsgId,
                        content: content,
                        role: 'user'
                    });
                });
            }
            
            if (copyBtn) {
                copyBtn.addEventListener('click', function() {
                    copyMessage(content);
                });
            }
            
            chatMessages.appendChild(messageElement);
            
            // 记录请求开始时间
            const startTime = new Date();
            
            // 显示正在输入指示器
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message message-assistant';
            typingDiv.innerHTML = `
                <div class="avatar-wrapper">
                    <img src="/static/images/ai_avatar.png" alt="AI" class="avatar">
                    <div class="avatar-name">AI助手</div>
                </div>
                <div>
                    <div class="message-bubble">
                        <div class="typing-indicator">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            `;
            chatMessages.appendChild(typingDiv);
            
            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            try {
                // 准备请求数据
                const requestData = {
                    conversation_id: currentConversationId,
                    content: finalContent,
                    use_web: useWebSwitch.checked ? 1 : 0,
                    function: currentFunction
                };
                
                // 添加功能信息
                if (currentFunction !== 'default') {
                    requestData.system_prompt = systemPrompt;
                }
                
                // 添加文件信息
                if (window.uploadedFiles && window.uploadedFiles.length > 0) {
                    // 获取第一个文件的ID作为主文档ID（兼容旧版本）
                    requestData.document_id = window.uploadedFiles[0].id;
                    // 获取所有文件的路径
                    requestData.file_paths = getUploadedFilePaths();
                }
                
                // 发送请求
                const response = await axios.post(`/api/v1/chat/messages/send`, requestData);
                
                // 更新消息元素的ID
                if (response.data.code === 200 && response.data.data.user_message) {
                    // 更新临时消息的真实ID
                    messageContainer.setAttribute('data-message-id', response.data.data.user_message.id);
                }
                
                // 计算响应时间
                const endTime = new Date();
                const responseTimeMs = endTime - startTime;
                const responseTimeStr = (responseTimeMs / 1000).toFixed(2) + 's';
                
                // 移除输入指示器
                chatMessages.removeChild(typingDiv);
                
                // 显示AI回复
                if (response.data.code === 200) {
                    const aiTemplate = document.getElementById('assistantMessageTemplate');
                    const aiMessageElement = aiTemplate.content.cloneNode(true);
                    const aiMessageContainer = aiMessageElement.querySelector('.message');
                    aiMessageContainer.setAttribute('data-message-id', response.data.data.ai_message.id);
                    
                    const aiContentDiv = aiMessageElement.querySelector('.message-content');
                    aiContentDiv.innerHTML = formatMessage(response.data.data.ai_message.content);
                    
                    // 添加到DOM
                    chatMessages.appendChild(aiMessageElement);
                    
                    // 获取真正添加到DOM的元素
                    const addedAiElement = chatMessages.querySelector(`[data-message-id="${response.data.data.ai_message.id}"]`);
                    
                    if (addedAiElement) {
                        // 设置思考过程(如果有)
                        setupThinkingProcess(addedAiElement, response.data.data.ai_message.thinking);
                        
                        // 设置消息状态
                        if (response.data.data.ai_message.status !== 'active') {
                            const statusDiv = addedAiElement.querySelector('.message-status');
                            if (statusDiv) {
                                statusDiv.textContent = response.data.data.ai_message.status === 'edited' ? '(已编辑)' : '(已删除)';
                                statusDiv.classList.add(response.data.data.ai_message.status);
                            }
                        }
                        
                        // 添加反馈按钮事件
                        addFeedbackActions(addedAiElement, response.data.data.ai_message);
                    }
                } else {
                    // 显示错误消息
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'message message-system text-danger';
                    errorDiv.innerHTML = `<div class="message-content">错误: ${response.data.message}</div>`;
                    chatMessages.appendChild(errorDiv);
                }
                
                // 滚动到底部
                chatMessages.scrollTop = chatMessages.scrollHeight;
                
                // 清除文件信息
                clearUploadedFiles();
            } catch (error) {
                console.error('发送消息失败', error);
                
                // 移除输入指示器
                chatMessages.removeChild(typingDiv);
                
                // 显示错误消息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'message message-system text-danger';
                errorDiv.innerHTML = `<div class="message-content">发送消息失败: ${error.response?.data?.message || '请重试'}</div>`;
                chatMessages.appendChild(errorDiv);
                
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }
        
        // 消息表单提交
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const content = messageInput.value.trim();
            if (content) {
                sendMessage(content);
                messageInput.value = '';
            }
        });
        
        // 新建对话按钮
        newChatBtn.addEventListener('click', function() {
            createNewConversation();
        });
        
        // 退出登录按钮
        logoutBtn.addEventListener('click', function() {
            localStorage.removeItem('token');
            window.location.href = '/login';
        });
        
        // 联网开关状态变化
        useWebSwitch.addEventListener('change', function() {
            // 状态已在HTML元素中保存
        });
        
        // 功能选择变化
        functionDropdown.addEventListener('change', function() {
            currentFunction = this.value;
        });
        
        // 功能选择下拉菜单点击事件
        document.querySelectorAll('.dropdown-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const functionType = this.getAttribute('data-function');
                currentFunction = functionType;
                
                // 更新下拉按钮文本
                const buttonText = functionType === 'default' ? '默认对话' : 
                                 functionType === 'news-search' ? '新闻搜索' : 
                                 functionType === 'writing-assistant' ? '帮我写作' : '选择功能';
                functionDropdown.textContent = buttonText;
                
                // 更新输入框提示
                if (functionType === 'news-search') {
                    messageInput.placeholder = '输入搜索问题，获取新闻资讯...';
                } else if (functionType === 'writing-assistant') {
                    messageInput.placeholder = '输入写作需求，AI帮你撰写内容...';
                } else {
                    messageInput.placeholder = '输入消息...';
                }
            });
        });
        
        // 初始化
        getUserInfo();
        loadConversations();
    });

    // 添加复制功能
    function copyMessage(content) {
        // 创建一个临时文本区域元素
        const textarea = document.createElement('textarea');
        textarea.value = content;
        document.body.appendChild(textarea);
        
        // 选择文本并复制
        textarea.select();
        document.execCommand('copy');
        
        // 移除临时元素
        document.body.removeChild(textarea);
        
        // 显示提示
        showToast('文本已复制到剪贴板', 'success');
    }

    // 编辑消息
    function editMessage(message) {
        const messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
        if (!messageElement) return;
        
        // 找到内容和操作元素
        const contentDiv = messageElement.querySelector('.message-content');
        const actionsDiv = messageElement.querySelector('.message-actions');
        
        // 保存原始内容和样式
        const originalContent = contentDiv.innerHTML;
        const originalDisplay = contentDiv.style.display;
        
        // 创建编辑元素和按钮
        const editElement = document.createElement('div');
        editElement.className = 'message-editing';
        editElement.innerHTML = `
            <div class="form-floating mb-2">
                <textarea class="form-control" style="height: 100px">${message.content}</textarea>
            </div>
            <div class="d-flex justify-content-end gap-2">
                <button class="btn btn-sm btn-outline-secondary cancel-btn">取消</button>
                <button class="btn btn-sm btn-primary save-btn">保存 & 重新生成</button>
            </div>
        `;
        
        // 先添加过渡样式
        contentDiv.style.transition = 'opacity 0.3s';
        if (actionsDiv) actionsDiv.style.transition = 'opacity 0.3s';
        
        // 隐藏内容和操作按钮
        contentDiv.style.opacity = '0';
        if (actionsDiv) actionsDiv.style.opacity = '0';
        
        // 延迟执行DOM操作，让过渡动画有效果
        setTimeout(() => {
            contentDiv.style.display = 'none';
            if (actionsDiv) actionsDiv.style.display = 'none';
            
            // 获取文本框和按钮
            const textarea = editElement.querySelector('textarea');
            const cancelBtn = editElement.querySelector('.cancel-btn');
            const saveBtn = editElement.querySelector('.save-btn');
            
            // 取消编辑
            cancelBtn.addEventListener('click', function() {
                // 移除编辑元素
                messageElement.removeChild(editElement);
                
                // 显示原始内容和按钮（带过渡动画）
                contentDiv.style.display = originalDisplay;
                if (actionsDiv) actionsDiv.style.display = 'flex';
                
                // 触发重绘后再过渡
                setTimeout(() => {
                    contentDiv.style.opacity = '1';
                    if (actionsDiv) actionsDiv.style.opacity = '1';
                }, 10);
            });
            
            // 保存编辑
            saveBtn.addEventListener('click', async function() {
                // 显示保存中状态
                saveBtn.disabled = true;
                saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
                
                try {
                    const response = await fetch(`/api/v1/chat/messages/edit`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('token')}`
                        },
                        body: JSON.stringify({ 
                            message_id: message.id,
                            content: textarea.value 
                        })
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        
                        if (result.code === 200) {
                            // 保存编辑后的消息
                            const editedMessage = result.data;
                            
                            // 更新内容
                            contentDiv.innerHTML = formatMessage(editedMessage.content);
                            
                            // 添加已编辑标记
                            const statusElement = messageElement.querySelector('.message-status');
                            if (statusElement) {
                                statusElement.textContent = '(已编辑)';
                                statusElement.classList.add('edited');
                            }
                            
                            // 移除编辑元素
                            messageElement.removeChild(editElement);
                            
                            // 恢复显示内容和按钮（带过渡动画）
                            contentDiv.style.display = originalDisplay;
                            if (actionsDiv) actionsDiv.style.display = 'flex';
                            
                            // 触发重绘后再过渡
                            setTimeout(() => {
                                contentDiv.style.opacity = '1';
                                if (actionsDiv) actionsDiv.style.opacity = '1';
                            }, 10);
                            
                            // 查找编辑消息后的AI回复并隐藏
                            let nextElement = messageElement.nextElementSibling;
                            if (nextElement && nextElement.classList.contains('message-assistant')) {
                                // 记录原始AI内容用于恢复（如果重生成失败）
                                const aiContentDiv = nextElement.querySelector('.message-content');
                                const originalAiContent = aiContentDiv.innerHTML;
                                
                                // 添加等待样式
                                nextElement.classList.add('waiting-regenerate');
                                aiContentDiv.innerHTML = '<div class="text-muted">正在根据编辑后的问题重新生成回答...</div>';
                                
                                // 隐藏反馈按钮
                                const feedbackButtons = nextElement.querySelector('.message-feedback');
                                if (feedbackButtons) {
                                    feedbackButtons.style.display = 'none';
                                }
                                
                                // 重新生成回答
                                try {
                                    // 保存当前问题的显示样式
                                    const currentQuestionDisplay = messageElement.style.display || '';
                                    messageElement.dataset.savedDisplay = currentQuestionDisplay;
                                    
                                    // 为该问题元素添加特殊标识类，确保重新生成后能找到它
                                    const uniqueClass = `edited-question-${Date.now()}`;
                                    messageElement.classList.add(uniqueClass);
                                    
                                    console.log('编辑后重新生成，问题ID:', message.id, '特殊标识类:', uniqueClass);
                                    const regenerated = await regenerateResponse(message.id);
                                    
                                    // 重新生成后无论成功与否，强制显示问题
                                    console.log('重新生成结果:', regenerated, '尝试恢复问题可见性');
                                    
                                    // 重新查找问题元素(防止DOM重构)
                                    const questionElement = document.querySelector(`.${uniqueClass}`) || messageElement;
                                    
                                    if (questionElement) {
                                        console.log('找到问题元素, 设置样式强制显示');
                                        // 强制显示问题的样式
                                        questionElement.style.cssText = `
                                            display: block !important; 
                                            visibility: visible !important; 
                                            opacity: 1 !important;
                                            position: relative !important;
                                            z-index: 100 !important;
                                        `;
                                        questionElement.classList.remove('waiting-regenerate');
                                    } else {
                                        console.error('无法找到问题元素:', uniqueClass);
                                    }
                                    
                                    if (!regenerated) {
                                        // 恢复原始AI内容
                                        nextElement.classList.remove('waiting-regenerate');
                                        aiContentDiv.innerHTML = originalAiContent;
                                        
                                        // 恢复反馈按钮
                                        if (feedbackButtons) {
                                            feedbackButtons.style.display = 'flex';
                                        }
                                    } else {
                                        // 如果重新生成成功，只隐藏原始AI回复，不隐藏用户问题
                                        nextElement.style.display = 'none';
                                    }
                                } catch (error) {
                                    console.error("重新生成回答失败:", error);
                                    // 恢复原始AI内容
                                    nextElement.classList.remove('waiting-regenerate');
                                    aiContentDiv.innerHTML = originalAiContent;
                                    
                                    // 恢复反馈按钮
                                    if (feedbackButtons) {
                                        feedbackButtons.style.display = 'flex';
                                    }
                                    
                                    showToast("重新生成回答失败", "error");
                                }
                            }
                        }
                    } else {
                        // 恢复原始状态
                        messageElement.removeChild(editElement);
                        contentDiv.style.display = originalDisplay;
                        if (actionsDiv) actionsDiv.style.display = 'flex';
                        
                        // 触发重绘后再过渡
                        setTimeout(() => {
                            contentDiv.style.opacity = '1';
                            if (actionsDiv) actionsDiv.style.opacity = '1';
                        }, 10);
                        
                        showToast("编辑消息失败", "error");
                    }
                } catch (error) {
                    // 恢复原始状态
                    messageElement.removeChild(editElement);
                    contentDiv.style.display = originalDisplay;
                    if (actionsDiv) actionsDiv.style.display = 'flex';
                    
                    // 触发重绘后再过渡
                    setTimeout(() => {
                        contentDiv.style.opacity = '1';
                        if (actionsDiv) actionsDiv.style.opacity = '1';
                    }, 10);
                    
                    console.error('编辑消息失败:', error);
                    showToast('编辑消息失败', 'error');
                }
            });
        }, 300); // 等待过渡效果完成
        
        messageElement.appendChild(editElement);
        
        // 自动聚焦文本框并将光标放在文本末尾
        setTimeout(() => {
            const textarea = editElement.querySelector('textarea');
            textarea.focus();
            textarea.selectionStart = textarea.selectionEnd = textarea.value.length;
        }, 350);
    }

    // 渲染消息
    function renderMessage(message) {
        const template = message.role === 'user' ? 
            document.getElementById('messageTemplate') : 
            document.getElementById('assistantMessageTemplate');
        
        const messageElement = template.content.cloneNode(true);
        const messageContainer = messageElement.querySelector('.message');
        const contentDiv = messageElement.querySelector('.message-content');
        
        // 防止XSS：使用formatMessage函数处理内容
        contentDiv.innerHTML = formatMessage(message.content || '');
        
        // 保存原始内容（用于恢复）
        contentDiv.dataset.originalContent = contentDiv.innerHTML;
        
        // 设置消息ID
        messageContainer.setAttribute('data-message-id', message.id);
        
        // 添加数据属性
        messageContainer.dataset.role = message.role;
        messageContainer.dataset.status = message.status || 'active';
        
        // 添加消息状态
        if (message.status && message.status !== 'active') {
            const statusElement = messageElement.querySelector('.message-status');
            if (statusElement) {
                statusElement.textContent = message.status === 'edited' ? '(已编辑)' : '(已删除)';
                statusElement.classList.add(message.status);
            }
        }
        
        // 添加操作按钮事件
        if (message.role === 'user') {
            const editBtn = messageElement.querySelector('.edit-btn');
            const copyBtn = messageElement.querySelector('.copy-btn');
            
            if (editBtn) {
                editBtn.addEventListener('click', () => editMessage(message));
            }
            
            if (copyBtn) {
                copyBtn.addEventListener('click', () => copyMessage(message.content));
            }
        } else {
            addFeedbackActions(messageElement, message);
        }
        
        return messageElement;
    }

    // 全局错误处理
    window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error || event.message);
        showToast(`系统错误: ${event.error?.message || event.message || '未知错误'}`, 'error');
    });

    // 处理Promise未捕获的异常
    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise异常:', event.reason);
        showToast(`系统错误: ${event.reason?.message || '未知Promise异常'}`, 'error');
    });
</script>
{% endblock %} 