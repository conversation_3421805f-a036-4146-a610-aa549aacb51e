import aiohttp
from typing import List, Dict, Any, Tuple
import re

from  core.config import LLM_API_URL, LLM_API_KEY

class AIService:
    """AI服务，用于与大模型API交互"""
    
    def __init__(self):
        self.api_url = LLM_API_URL
        self.api_key = LLM_API_KEY
        self.headers = {
            "Authorization": self.api_key,
            "Content-Type": "application/json"
        }
    
    async def get_ai_response(self, messages: List[Dict[str, str]], 
                        temperature: float = 0.7, 
                        max_tokens: int = 10000) -> Dict[str, Any]:
        """
        获取AI大模型的回复
        
        参数:
            messages: 消息列表，格式为[{"role": "user", "content": "你好"}, ...]
            temperature: 温度参数，控制输出的随机性
            max_tokens: 返回的最大token数
            
        返回:
            Dict[str, Any]: 包含AI回复内容的字典，新增thinking字段表示思考过程
            
        异常:
            直接抛出请求过程中的异常，由调用者处理
        """
        if not messages:
            raise ValueError("消息列表为空")
            
        data = {
            "messages": messages,
            "temperature": temperature,
            "top_p": 1,
            "frequency_penalty": 0,
            "max_tokens": max_tokens
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(self.api_url, headers=self.headers, json=data, timeout=60*5) as response:
                if response.status == 200:
                    result = await response.json()
                    content, thinking = self._extract_content_and_thinking(result)
                    if content is None:
                        raise ValueError("无法从API响应中提取内容")
                    return {"content": content, "thinking": thinking, "original_response": result}
                else:
                    error_text = await response.text()
                    raise ValueError(f"API错误: 状态码 {response.status}, 详情: {error_text}")
    
    def _extract_content_and_thinking(self, response: Dict[str, Any]) -> Tuple[str, str]:
        """从API响应中提取内容和思考过程"""
        content = None
        thinking = None
        
        if "choices" in response and len(response["choices"]) > 0:
            message = response["choices"][0].get("message", {})
            raw_content = message.get("content", "")
            
            # 提取思考过程 <think>...</think>
            think_pattern = re.compile(r'<think>(.*?)</think>', re.DOTALL)
            think_matches = think_pattern.findall(raw_content)
            
            if think_matches:
                thinking = '\n'.join(think_matches)
                # 从内容中移除<think>标签及其内容
                content = re.sub(r'<think>.*?</think>', '', raw_content, flags=re.DOTALL).strip()
            else:
                content = raw_content
                
        return content, thinking

# 创建单例实例
ai_service = AIService() 