from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Query, Depends, Request
from datetime import datetime, timedelta, date
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from models import User, get_db
from core.security import get_current_active_user
from services.statistics_service import (
    get_user_summary,
    get_visits_summary,
    get_questions_summary,
    get_trend_data,
)
from core.response import success_response

router = APIRouter()


class VisitsData(BaseModel):
    total: int = Field(..., description="累计访问次数")
    today: int = Field(..., description="今日访问次数")


class QuestionsData(BaseModel):
    total: int = Field(..., description="累计提问次数")
    today: int = Field(..., description="今日提问次数")


class UsersData(BaseModel):
    total_accounts: int = Field(..., description="系统中注册用户总数")
    total_logged_in: int = Field(..., description="历史上至少登录过一次的用户数量")
    today_logged_in: int = Field(..., description="今天登录的用户数量")
    online: int = Field(..., description="当前在线用户数量")


class TrendDataItem(BaseModel):
    time: str = Field(
        ...,
        description="时间点，按 group_by 决定精度，格式为 YYYY-MM-DD HH:00 或 YYYY-MM-DD 或 YYYY-MM",
    )
    visits: int = Field(..., description="访问次数")
    questions: int = Field(..., description="提问次数")


class SummaryData(BaseModel):
    visits: VisitsData = Field(..., description="访问统计数据")
    questions: QuestionsData = Field(..., description="提问统计数据")
    users: UsersData = Field(..., description="用户统计数据")
    stat_date: str = Field(..., description="数据统计日期，格式为YYYY-MM-DD")


@router.get(
    "/trend",
    response_model=Dict[str, Any],
    summary="获取趋势数据",
    description="根据指定的时间范围和分组方式，获取平台访问量和提问量的趋势数据",
)
async def get_trend_data_api(
    request: Request,
    group_by: str = Query(
        ..., description="分组方式，可选值为 hour(按小时)、day(按天)或month(按月)"
    ),
    start_date: Optional[str] = Query(
        None, description="起始日期，格式为 YYYY-MM-DD，若不指定则默认为最近7天"
    ),
    end_date: Optional[str] = Query(
        None, description="结束日期，格式为 YYYY-MM-DD，若不指定则默认为当天"
    ),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    获取平台访问和提问趋势数据

    - **group_by**: 分组方式，可选值为 hour(按小时)、day(按天)或month(按月)
    - **start_date**: 起始日期，格式为 YYYY-MM-DD
    - **end_date**: 结束日期，格式为 YYYY-MM-DD
    - 若未指定日期范围，则默认返回最近 7 天的按天数据

    **返回参数说明**:
    - **code**: 状态码，200表示成功
    - **message**: 操作结果描述
    - **data**: 趋势数据列表，每项包含：
      - **time**: 时间点，按分组方式决定精度（hour: YYYY-MM-DD HH:00, day: YYYY-MM-DD, month: YYYY-MM）
      - **visits**: 该时间点的访问次数
      - **questions**: 该时间点的提问次数

    **返回示例**:
    ```json
    {
        "code": 200,
        "message": "操作成功",
        "data": [
            {
                "time": "2023-01-01",
                "visits": 120,
                "questions": 45
            },
            {
                "time": "2023-01-02",
                "visits": 150,
                "questions": 60
            }
        ]
    }
    ```
    """
    try:
        # 默认返回最近7天数据
        if not start_date:
            end_date_obj = datetime.now().date()
            start_date_obj = end_date_obj - timedelta(days=7)
            start_date = start_date_obj.strftime("%Y-%m-%d")
            end_date = end_date_obj.strftime("%Y-%m-%d")
        else:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()

        # 对于按月分组，设置合理的默认时间范围（如果未指定）
        if group_by == "month" and not start_date:
            end_date_obj = datetime.now().date()
            # 默认返回最近12个月的数据
            start_date_obj = date(end_date_obj.year - 1, end_date_obj.month, 1)
            start_date = start_date_obj.strftime("%Y-%m-%d")
            end_date = end_date_obj.strftime("%Y-%m-%d")

        # 获取趋势数据
        trend_data = get_trend_data(db, group_by, start_date_obj, end_date_obj)

        return success_response(data=trend_data)
    except ValueError as e:
        return {"code": 400, "message": str(e), "data": None}


@router.get(
    "/summary",
    response_model=Dict[str, Any],
    summary="获取平台统计汇总",
    description="获取平台访问量、提问量和用户数据的统计汇总信息",
)
async def get_platform_summary(
    request: Request,
    module: Optional[str] = Query(
        None, description="模块名（如 pc、h5 等），用于筛选特定模块的统计数据"
    ),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    获取平台统计汇总数据

    - **module**: 可选参数，模块名（如 pc、h5 等），用于筛选特定模块的统计数据

    **返回参数说明**:
    - **code**: 状态码，200表示成功
    - **message**: 操作结果描述
    - **data**: 统计汇总数据
      - **visits**: 访问统计
        - **total**: 累计访问次数
        - **today**: 今日访问次数
      - **questions**: 提问统计
        - **total**: 累计提问次数
        - **today**: 今日提问次数
      - **users**: 用户统计
        - **total_accounts**: 系统中注册用户总数
        - **total_logged_in**: 历史上至少登录过一次的用户数量
        - **today_logged_in**: 今天登录的用户数量
        - **online**: 当前在线用户数量
      - **stat_date**: 数据统计日期，格式为YYYY-MM-DD

    **返回示例**:
    ```json
    {
        "code": 200,
        "message": "操作成功",
        "data": {
            "visits": {
                "total": 5000,
                "today": 120
            },
            "questions": {
                "total": 2000,
                "today": 50
            },
            "users": {
                "total_accounts": 500,
                "total_logged_in": 300,
                "today_logged_in": 30,
                "online": 10
            },
            "stat_date": "2023-01-15"
        }
    }
    ```
    """
    # 获取当前日期
    today_date = date.today().strftime("%Y-%m-%d")

    # 获取统计数据
    visits_data = get_visits_summary(db)
    questions_data = get_questions_summary(db)
    users_data = get_user_summary(db)

    # 构建返回数据
    summary_data = {
        "visits": visits_data,
        "questions": questions_data,
        "users": users_data,
        "stat_date": today_date,
    }

    # 如果指定了模块，可以根据模块筛选数据
    if module:
        # 这里保留模块参数的接口，实际实现暂不过滤
        pass

    return success_response(data=summary_data)
