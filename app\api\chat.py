from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, UploadFile, File
from sqlalchemy.orm import Session
import shutil
from pathlib import Path as PathLib
from datetime import datetime
import asyncio

from models import get_db, User, Conversation, Message, Document
from core.security import get_current_active_user, get_conversation_for_user
from services.db_service import (
    get_conversations, create_conversation, get_conversation, 
    update_conversation, delete_conversation, get_messages, create_message, get_message, edit_message,
    create_message_feedback, create_document
)
from services.ai_service import ai_service
from core.response import success_response, error_response, StandardResponse

from pydantic import BaseModel, Field

router = APIRouter()

# 基础模型定义
class MessageModel(BaseModel):
    """消息模型"""
    id: int = Field(..., description="消息ID")
    role: str = Field(..., description="角色，user-用户，assistant-AI助手")
    content: str = Field(..., description="消息内容")
    created_at: str = Field(..., description="创建时间，ISO 8601格式")
    file_paths: Optional[List[str]] = Field(None, description="关联文件路径列表")
    use_web: Optional[int] = Field(0, description="是否使用了网络搜索，0-不使用，1-使用")
    status: Optional[str] = Field("active", description="消息状态：active-活跃，edited-已编辑，deleted-已删除")
    parent_message_id: Optional[int] = Field(None, description="父消息ID，用于编辑历史")
    thinking: Optional[str] = Field(None, description="AI助手的思考过程")

class MessageCreate(BaseModel):
    """消息创建请求"""
    content: str = Field(..., description="消息内容")
    document_id: Optional[int] = Field(None, description="关联的文档ID")
    file_paths: Optional[List[str]] = Field(None, description="上传文件的路径列表")
    use_web: Optional[int] = Field(0, description="是否使用网络搜索，0-不使用，1-使用")

class MessageEdit(BaseModel):
    """消息编辑请求"""
    message_id: int = Field(..., description="消息ID")
    content: str = Field(..., description="新的消息内容")

class MessageFeedbackCreate(BaseModel):
    """消息反馈请求"""
    message_id: int = Field(..., description="消息ID")
    feedback_type: str = Field(..., description="反馈类型：like-点赞，dislike-差评，regenerate-重新生成")

class ConversationModel(BaseModel):
    """对话模型"""
    id: int = Field(..., description="对话ID")
    title: str = Field(..., description="对话标题")
    created_at: str = Field(..., description="创建时间，ISO 8601格式")
    updated_at: str = Field(..., description="最后更新时间，ISO 8601格式")

class ConversationCreate(BaseModel):
    """对话创建请求"""
    title: str = Field("新对话", description="对话标题")

class ConversationUpdate(BaseModel):
    """对话更新请求"""
    conversation_id: int = Field(..., description="对话ID")
    title: str = Field(..., description="新的对话标题")

class ConversationDelete(BaseModel):
    """对话删除请求"""
    conversation_id: int = Field(..., description="对话ID")

class ConversationDetail(BaseModel):
    """对话详情请求"""
    conversation_id: int = Field(..., description="对话ID")

# 响应模型定义
class ConversationListResponse(BaseModel):
    """对话列表响应"""
    code: int = Field(200, description="状态码，200表示成功")
    message: str = Field("操作成功", description="操作结果描述")
    data: List[ConversationModel] = Field(..., description="对话列表数据")

class ConversationDetailResponse(BaseModel):
    """对话详情响应"""
    code: int = Field(200, description="状态码，200表示成功")
    message: str = Field("操作成功", description="操作结果描述")
    data: Dict[str, Any] = Field(..., description="对话详情数据，包含id、title、created_at、updated_at和messages字段")

class ConversationCreateResponse(BaseModel):
    """对话创建响应"""
    code: int = Field(200, description="状态码，200表示成功")
    message: str = Field("对话创建成功", description="操作结果描述")
    data: ConversationModel = Field(..., description="新创建的对话信息")

class ConversationUpdateResponse(BaseModel):
    """对话更新响应"""
    code: int = Field(200, description="状态码，200表示成功")
    message: str = Field("对话标题更新成功", description="操作结果描述")
    data: ConversationModel = Field(..., description="更新后的对话信息")

class ConversationDeleteResponse(BaseModel):
    """对话删除响应"""
    code: int = Field(200, description="状态码，200表示成功")
    message: str = Field("对话已删除", description="操作结果描述")
    data: None = Field(None, description="无数据返回")

class DocumentResponse(BaseModel):
    """文档上传响应"""
    code: int = Field(200, description="状态码，200表示成功")
    message: str = Field("文件上传成功", description="操作结果描述")
    data: Dict[str, Any] = Field(..., description="上传文件的信息")

class MessageData(BaseModel):
    """消息数据模型"""
    user_message: MessageModel = Field(..., description="用户发送的消息")
    ai_message: Optional[MessageModel] = Field(None, description="AI回复的消息")

class MessageCreateResponse(BaseModel):
    """消息创建响应"""
    code: int = Field(200, description="状态码，200表示成功")
    message: str = Field("消息发送成功", description="操作结果描述")
    data: MessageData = Field(..., description="消息数据，包含用户消息和AI回复")

class MessageEditResponse(BaseModel):
    """消息编辑响应"""
    code: int = Field(200, description="状态码，200表示成功")
    message: str = Field("消息编辑成功", description="操作结果描述")
    data: MessageModel = Field(..., description="编辑后的消息信息")

class MessageRegenerateResponse(BaseModel):
    """重新生成回答响应"""
    code: int = Field(200, description="状态码，200表示成功")
    message: str = Field("回答已重新生成", description="操作结果描述")
    data: MessageModel = Field(..., description="新生成的AI回答")

class RegenerateRequest(BaseModel):
    """重新生成请求"""
    message_id: int = Field(..., description="消息ID，支持AI消息ID或用户消息ID")

class MessageSend(BaseModel):
    """发送消息请求"""
    conversation_id: int = Field(..., description="对话ID")
    content: str = Field(..., description="消息内容")
    document_id: Optional[int] = Field(None, description="关联的文档ID")
    file_paths: Optional[List[str]] = Field(None, description="上传文件的路径列表")
    use_web: Optional[int] = Field(0, description="是否使用网络搜索，0-不使用，1-使用")
    system_prompt: Optional[str] = Field(None, description="系统提示词，用于设置AI回复的角色和风格")

# 文件上传接口
@router.post("/upload", response_model=DocumentResponse, summary="上传文件", description="上传文件并返回文件信息")
async def upload_file(
    file: UploadFile = File(..., description="要上传的文件"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    上传文件接口
    
    参数：
    - file: 要上传的文件
    
    返回：
    - code: 状态码，200表示成功
    - message: 操作结果描述
    - data: 上传文件的信息，包含id、filename、file_path等
    """
    try:
        # 检查app/uploads目录是否存在，不存在则创建
        uploads_dir = PathLib("uploads")
        if not uploads_dir.exists():
            uploads_dir.mkdir(parents=True)
        
        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        unique_filename = f"{current_user.id}_{timestamp}_{file.filename}"
        file_path = f"uploads/{unique_filename}"  # 这是储存在数据库的相对路径，前端使用
        
        # 保存文件
        file_location = uploads_dir / unique_filename
        with open(file_location, "wb+") as file_object:
            shutil.copyfileobj(file.file, file_object)
        
        # 保存文件记录到数据库
        document = create_document(
            db=db,
            user_id=current_user.id,
            filename=file.filename,
            file_path=file_path,
            content_type=file.content_type
        )
        
        # 返回响应
        data = {
            "id": document.id,
            "filename": document.filename,
            "file_path": document.file_path,
            "content_type": document.content_type,
            "created_at": document.created_at.isoformat()
        }
        
        return success_response(data=data, message="文件上传成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

# 对话相关接口
@router.get("/conversations", response_model=ConversationListResponse, summary="获取对话列表", description="获取当前用户的所有对话")
def get_user_conversations(
    skip: int = Query(0, description="分页起始位置"),
    limit: int = Query(100, description="每页数量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    获取用户的所有对话列表
    
    返回：
    - code: 状态码，200表示成功
    - message: 操作结果描述
    - data: 对话列表，每项包含id、title、created_at、updated_at
    """
    conversations = get_conversations(db, user_id=current_user.id, skip=skip, limit=limit)
    data = [
        {
            "id": conv.id,
            "title": conv.title,
            "created_at": conv.created_at.isoformat(),
            "updated_at": conv.updated_at.isoformat()
        }
        for conv in conversations
    ]
    return success_response(data=data)

@router.post("/conversations", response_model=ConversationCreateResponse, summary="创建新对话", description="创建一个新的对话")
def create_new_conversation(
    conversation: ConversationCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    创建新对话
    
    参数：
    - title: 对话标题，默认为"新对话"
    
    返回：
    - code: 状态码，200表示成功
    - message: 操作结果描述
    - data: 新创建的对话信息，包含id、title、created_at、updated_at
    """
    db_conversation = create_conversation(db, user_id=current_user.id, title=conversation.title)
    data = {
        "id": db_conversation.id,
        "title": db_conversation.title,
        "created_at": db_conversation.created_at.isoformat(),
        "updated_at": db_conversation.updated_at.isoformat()
    }
    return success_response(data=data, message="对话创建成功")

@router.post("/conversations/detail", response_model=ConversationDetailResponse, summary="获取对话详情", description="获取指定对话的详细信息，包括所有消息")
def get_conversation_detail(
    conversation_detail: ConversationDetail,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    获取对话详情，包括对话中的所有消息
    
    参数：
    - conversation_id: 对话ID
    
    返回：
    - code: 状态码，200表示成功
    - message: 操作结果描述
    - data: 对话详情，包含id、title、created_at、updated_at和messages数组
    """
    # 获取对话并验证权限
    conversation = get_conversation(db, conversation_id=conversation_detail.conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="对话不存在")
    if conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限访问此对话")
    
    messages = get_messages(db, conversation_id=conversation.id)
    data = {
        "id": conversation.id,
        "title": conversation.title,
        "created_at": conversation.created_at.isoformat(),
        "updated_at": conversation.updated_at.isoformat(),
        "messages": [
            {
                "id": msg.id,
                "role": msg.role,
                "content": msg.content,
                "created_at": msg.created_at.isoformat(),
                "file_paths": msg.file_paths,
                "use_web": msg.use_web,
                "status": msg.status,
                "parent_message_id": msg.parent_message_id,
                "thinking": msg.thinking if msg.role == "assistant" else None
            }
            for msg in messages
        ]
    }
    return success_response(data=data)

@router.put("/conversations/update", response_model=ConversationUpdateResponse, summary="更新对话标题", description="更新指定对话的标题")
def update_conversation_title(
    conversation_update: ConversationUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    更新对话标题
    
    参数：
    - conversation_id: 对话ID
    - title: 新的对话标题
    
    返回：
    - code: 状态码，200表示成功
    - message: 操作结果描述
    - data: 更新后的对话信息，包含id、title、created_at、updated_at
    """
    # 获取对话并验证权限
    conversation = get_conversation(db, conversation_id=conversation_update.conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="对话不存在")
    if conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限操作此对话")
    
    db_conversation = update_conversation(db, conversation_id=conversation.id, title=conversation_update.title)
    data = {
        "id": db_conversation.id,
        "title": db_conversation.title,
        "created_at": db_conversation.created_at.isoformat(),
        "updated_at": db_conversation.updated_at.isoformat()
    }
    return success_response(data=data, message="对话标题更新成功")

@router.post("/conversations/delete", response_model=ConversationDeleteResponse, summary="删除对话", description="删除指定的对话及所有关联数据")
def delete_user_conversation(
    conversation_delete: ConversationDelete,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    删除对话及其所有相关数据
    
    本接口会删除：
    - 指定的对话记录
    - 对话中的所有消息
    - 消息相关的所有反馈记录
    
    参数：
    - conversation_id: 对话ID
    
    返回：
    - code: 状态码，200表示成功
    - message: 操作结果描述
    - data: null
    """
    # 获取对话并验证权限
    conversation = get_conversation(db, conversation_id=conversation_delete.conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="对话不存在")
    if conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限操作此对话")
    
    if delete_conversation(db, conversation_id=conversation.id):
        return success_response(message="对话已删除")
    else:
        raise HTTPException(status_code=500, detail="删除对话失败")

# 消息相关接口
@router.post("/messages/send", response_model=MessageCreateResponse, summary="发送消息", description="向指定对话发送消息并获取AI回复")
async def create_message_and_get_response(
    message: MessageSend,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    发送消息并获取AI回复
    
    参数：
    - conversation_id: 对话ID
    - content: 消息内容
    - document_id: 关联的文档ID，可选
    - file_paths: 上传文件的路径列表，可选
    - use_web: 是否使用网络搜索，0-不使用，1-使用，默认为0
    
    返回：
    - code: 状态码，200表示成功
    - message: 操作结果描述
    - data: 消息数据，包含user_message和ai_message
    """
    # 获取对话并验证权限
    print(message)
    conversation = get_conversation(db, conversation_id=message.conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="对话不存在")
    if conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限操作此对话")
    
    # 创建用户消息
    user_message = create_message(
        db=db,
        conversation_id=conversation.id,
        role="user",
        content=message.content,
        file_paths=message.file_paths,
        use_web=message.use_web
    )
    
    # 获取对话历史
    messages = get_messages(db, conversation_id=conversation.id)
    history = [
        {"role": msg.role, "content": msg.content}
        for msg in messages
    ]
    
    # 如果有系统提示词，添加到history的开头
    if message.system_prompt:
        history.insert(0, {"role": "system", "content": message.system_prompt})
    print(history)
    # 调用AI服务获取回复
    try:
        # 调用AI服务获取回复
        ai_response = await ai_service.get_ai_response(history)
        
        # 创建AI回复消息
        ai_message = create_message(
            db=db,
            conversation_id=conversation.id,
            role="assistant",
            content=ai_response["content"],
            thinking=ai_response.get("thinking")
        )
        
        data = {
            "user_message": {
                "id": user_message.id,
                "role": user_message.role,
                "content": user_message.content,
                "created_at": user_message.created_at.isoformat(),
                "file_paths": user_message.file_paths,
                "use_web": user_message.use_web,
                "status": user_message.status,
                "parent_message_id": user_message.parent_message_id
            },
            "ai_message": {
                "id": ai_message.id,
                "role": ai_message.role,
                "content": ai_message.content,
                "created_at": ai_message.created_at.isoformat(),
                "file_paths": ai_message.file_paths,
                "use_web": ai_message.use_web,
                "status": ai_message.status,
                "parent_message_id": ai_message.parent_message_id,
                "thinking": ai_message.thinking
            }
        }
        
        return success_response(data=data, message="消息发送成功")
    except Exception as e:
        # 如果AI响应失败，只返回用户消息
        data = {
            "user_message": {
                "id": user_message.id,
                "role": user_message.role,
                "content": user_message.content,
                "created_at": user_message.created_at.isoformat(),
                "file_paths": user_message.file_paths,
                "use_web": user_message.use_web,
                "status": user_message.status,
                "parent_message_id": user_message.parent_message_id
            }
        }
        
        return error_response(code=500, message=f"AI响应失败: {str(e)}", data=data)

@router.put("/messages/edit", response_model=MessageEditResponse, summary="编辑消息", description="编辑指定的消息")
def edit_user_message(
    message_edit: MessageEdit,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    编辑消息，会创建一个新的消息并将原消息标记为已编辑
    
    参数：
    - message_id: 消息ID
    - content: 新的消息内容
    
    返回：
    - code: 状态码，200表示成功
    - message: 操作结果描述
    - data: 编辑后的消息信息
    """
    # 获取原消息
    original_message = get_message(db, message_id=message_edit.message_id)
    if not original_message:
        raise HTTPException(status_code=404, detail="消息不存在")
    
    # 检查消息是否属于当前用户
    conversation = get_conversation(db, conversation_id=original_message.conversation_id)
    if conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权编辑此消息")
    
    # 编辑消息
    edited_message = edit_message(db, message_id=message_edit.message_id, new_content=message_edit.content)
    if not edited_message:
        raise HTTPException(status_code=500, detail="编辑消息失败")
    
    data = {
        "id": edited_message.id,
        "role": edited_message.role,
        "content": edited_message.content,
        "created_at": edited_message.created_at.isoformat(),
        "file_paths": edited_message.file_paths,
        "use_web": edited_message.use_web,
        "status": edited_message.status,
        "parent_message_id": edited_message.parent_message_id
    }
    
    return success_response(data=data, message="消息编辑成功")

@router.post("/messages/regenerate", response_model=MessageRegenerateResponse, summary="重新生成回答", description="重新生成AI对指定问题的回答")
async def regenerate_ai_response(
    regenerate_request: RegenerateRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    重新生成AI回答，支持根据用户消息ID或AI消息ID重新生成
    
    参数：
    - message_id: 消息ID，支持AI消息ID或用户消息ID
    
    返回：
    - code: 状态码，200表示成功
    - message: 操作结果描述
    - data: 新生成的AI回答
    """
    # 获取原消息
    original_message = get_message(db, message_id=regenerate_request.message_id)
    if not original_message:
        raise HTTPException(status_code=404, detail="消息不存在")
    
    # 检查消息是否属于当前用户
    conversation = get_conversation(db, conversation_id=original_message.conversation_id)
    if conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权重新生成此回答")
    
    # 处理已编辑的消息
    if original_message.status == 'edited':
        edited_message = db.query(Message).filter(
            Message.parent_message_id == original_message.id,
            Message.status == 'active'
        ).first()
        
        if edited_message:
            original_message = edited_message
    
    # 确定用户问题和要替换的AI消息
    user_message = None
    ai_message_to_replace = None
    
    # 获取对话所有活跃消息
    messages = db.query(Message).filter(
        Message.conversation_id == conversation.id,
        Message.status.in_(['active', 'edited'])
    ).order_by(Message.created_at).all()
    
    # 根据消息角色处理
    if original_message.role == "user":
        # 使用用户消息
        user_message = original_message
        
        # 找到该用户消息之后的AI回复
        for msg in messages:
            if (msg.created_at > user_message.created_at and 
                msg.role == "assistant" and
                msg.status == 'active'):
                ai_message_to_replace = msg
                break
                
    elif original_message.role == "assistant":
        # 使用AI消息
        ai_message_to_replace = original_message
        
        # 找到对应的用户问题
        for i in range(len(messages)-1, -1, -1):
            if messages[i].id == original_message.id:
                for j in range(i-1, -1, -1):
                    if j >= 0 and messages[j].role == "user":
                        user_message = messages[j]
                        break
                break
    
    # 如果找不到用户消息，获取最后一条用户消息
    if not user_message:
        last_user_message = db.query(Message).filter(
            Message.conversation_id == conversation.id,
            Message.role == "user",
            Message.status.in_(['active', 'edited'])
        ).order_by(Message.created_at.desc()).first()
        
        if last_user_message:
            user_message = last_user_message
    
    # 如果找不到用户消息，返回错误
    if not user_message:
        raise HTTPException(status_code=400, detail="找不到对应的用户问题")
    
    # 准备对话历史
    history = []
    user_message_added = False
    
    # 构建对话历史
    for msg in messages:
        if msg.id == user_message.id:
            history.append({"role": msg.role, "content": msg.content})
            user_message_added = True
            break
        elif msg.created_at < user_message.created_at:
            history.append({"role": msg.role, "content": msg.content})
    
    # 确保用户消息被添加
    if not user_message_added:
        history.append({"role": user_message.role, "content": user_message.content})
    
    # 如果历史为空，返回错误
    if not history:
        raise HTTPException(status_code=400, detail="无法构建有效的对话历史")
    
    # 等待数据库事务完成
    await asyncio.sleep(0.2)
    
    # 处理编辑后重新生成的场景
    if not ai_message_to_replace and user_message.parent_message_id is not None:
        original_user_message = get_message(db, message_id=user_message.parent_message_id)
        if original_user_message:
            for msg in messages:
                if (msg.created_at > original_user_message.created_at and 
                    msg.created_at < user_message.created_at and
                    msg.role == "assistant" and
                    msg.status == 'active'):
                    ai_message_to_replace = msg
                    break
    
    # 将传入消息后的所有消息标记为deleted，但排除用户问题
    reference_message = user_message
    # 如果是修改后的消息，则使用原始消息作为参考点
    if user_message.parent_message_id is not None:
        original_message = get_message(db, message_id=user_message.parent_message_id)
        if original_message:
            reference_message = original_message
    
    for msg in messages:
        # 只处理参考消息之后的消息，且不能是当前用户消息
        if msg.created_at > reference_message.created_at and msg.id != user_message.id:
            msg.status = 'deleted'
    
    # 提交对消息状态的更改
    db.commit()
    
    # 调用AI服务获取新回复
    try:
        # 获取AI回复
        ai_response = await ai_service.get_ai_response(history)
        
        # 创建新的AI回复
        new_ai_message = create_message(
            db=db,
            conversation_id=conversation.id,
            role="assistant",
            content=ai_response["content"],
            parent_message_id=ai_message_to_replace.id if ai_message_to_replace else None,
            thinking=ai_response.get("thinking")
        )
        
        # 记录重新生成反馈
        create_message_feedback(
            db=db,
            message_id=regenerate_request.message_id,
            user_id=current_user.id,
            feedback_type="regenerate"
        )
        
        data = {
            "id": new_ai_message.id,
            "role": new_ai_message.role,
            "content": new_ai_message.content,
            "created_at": new_ai_message.created_at.isoformat(),
            "file_paths": new_ai_message.file_paths,
            "use_web": new_ai_message.use_web,
            "status": new_ai_message.status,
            "parent_message_id": new_ai_message.parent_message_id,
            "thinking": new_ai_message.thinking
        }
        
        return success_response(data=data, message="回答已重新生成")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重新生成回答失败: {str(e)}")

@router.post("/messages/feedback", summary="添加消息反馈", description="对消息添加反馈（点赞、差评）")
def add_message_feedback(
    feedback: MessageFeedbackCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    添加消息反馈，如点赞、差评等
    
    参数：
    - message_id: 消息ID
    - feedback_type: 反馈类型，可选值：like(点赞)、dislike(差评)、regenerate(重新生成)
    
    返回：
    - code: 状态码，200表示成功
    - message: 操作结果描述
    - data: 无数据返回
    """
    # 获取消息
    message = get_message(db, message_id=feedback.message_id)
    if not message:
        raise HTTPException(status_code=404, detail="消息不存在")
    
    # 检查消息是否属于当前用户的对话
    conversation = get_conversation(db, conversation_id=message.conversation_id)
    if conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权对此消息添加反馈")
    
    # 添加反馈
    create_message_feedback(
        db=db,
        message_id=feedback.message_id,
        user_id=current_user.id,
        feedback_type=feedback.feedback_type
    )
    
    return success_response(message=f"已添加{feedback.feedback_type}反馈") 