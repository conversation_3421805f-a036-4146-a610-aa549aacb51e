from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from pydantic import BaseModel

from models import get_db, User
from core.security import get_current_active_user
from services.statistics_service import log_user_event
from core.response import success_response

router = APIRouter()

class TrackEventRequest(BaseModel):
    """跟踪事件请求模型"""
    page: str  # 页面标识
    user_id: Optional[int] = None  # 用户ID，可选（若通过Token获取）
    timestamp: Optional[str] = None  # 时间戳，可选（服务端生成）
    detail: Optional[Dict[str, Any]] = None  # 额外信息，可选

@router.post("/visit", response_model=Dict[str, Any], summary="记录页面访问事件")
async def track_visit(
    request: Request,
    track_data: TrackEventRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """
    记录用户访问页面事件
    
    - **page**: 页面标识，例如 chat, login 等
    - **user_id**: 用户ID (可选，默认使用当前登录用户)
    - **timestamp**: 时间戳 (可选，服务端自动生成)
    - **detail**: 事件详情 (可选)
    
    **返回参数说明**:
    - **code**: 状态码，200表示成功
    - **message**: 操作结果描述
    - **data**: 通常为null
    
    **返回示例**:
    ```json
    {
        "code": 200,
        "message": "事件记录成功",
        "data": null
    }
    ```
    """
    # 使用当前用户ID
    user_id = current_user.id
    
    # 获取客户端信息
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("user-agent")
    
    # 记录事件
    log_user_event(
        db=db,
        user_id=user_id,
        event_type="visit",
        page=track_data.page,
        ip_address=ip_address,
        user_agent=user_agent,
        detail=track_data.detail
    )
    
    return success_response(message="事件记录成功")
    
@router.post("/event", response_model=Dict[str, Any], summary="记录自定义事件")
async def track_event(
    request: Request,
    event_type: str,
    track_data: TrackEventRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """
    记录自定义用户事件
    
    - **event_type**: 事件类型，如 click, search 等
    - **page**: 页面标识，例如 chat, login 等
    - **user_id**: 用户ID (可选，默认使用当前登录用户)
    - **timestamp**: 时间戳 (可选，服务端自动生成)
    - **detail**: 事件详情 (可选)
    
    **返回参数说明**:
    - **code**: 状态码，200表示成功
    - **message**: 操作结果描述
    - **data**: 通常为null
    
    **返回示例**:
    ```json
    {
        "code": 200,
        "message": "事件记录成功",
        "data": null
    }
    ```
    """
    # 使用当前用户ID
    user_id = current_user.id
    
    # 获取客户端信息
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("user-agent")
    
    # 记录事件
    log_user_event(
        db=db,
        user_id=user_id,
        event_type=event_type,
        page=track_data.page,
        ip_address=ip_address,
        user_agent=user_agent,
        detail=track_data.detail
    )
    
    return success_response(message="事件记录成功") 