import os
from dotenv import load_dotenv
from pathlib import Path

# 获取项目根目录并加载.env文件
BASE_DIR = Path(__file__).resolve().parent.parent.parent
env_path = os.path.join(BASE_DIR, '.env')
load_dotenv(dotenv_path=env_path)

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL")

# 安全配置
SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = os.getenv("ALGORITHM")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES")) if os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES") else 720  # 12小时

# 大模型API配置
LLM_API_URL = os.getenv("LLM_API_URL")
LLM_API_KEY = os.getenv("LLM_API_KEY")

# 应用配置
APP_NAME = "深圳能源AI助手"
API_VERSION = "v1" 